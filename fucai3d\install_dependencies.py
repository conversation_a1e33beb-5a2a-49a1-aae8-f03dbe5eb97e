#!/usr/bin/env python3
"""
P6-P7预测器依赖安装脚本

为个人Python 3.11.9开发环境安装必需的依赖包
不使用Anaconda或Docker，使用标准pip

使用方法:
python install_dependencies.py
"""

import sys
import subprocess
import importlib

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"📦 {title}")
    print('='*60)

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name, description=""):
    """安装单个包"""
    print(f"⏳ 安装 {package_name}...")
    if description:
        print(f"   {description}")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(f"   {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {e}")
        return False

def install_basic_dependencies():
    """安装基础依赖"""
    print_header("安装基础依赖 (必需)")
    
    basic_packages = [
        ('pandas', 'pandas', '数据处理和分析库'),
        ('numpy', 'numpy', '数值计算库'),
        ('PyYAML', 'yaml', '配置文件处理库')
    ]
    
    success_count = 0
    for package_name, import_name, description in basic_packages:
        if check_package(import_name):
            print(f"✅ {package_name} 已安装")
            success_count += 1
        else:
            if install_package(package_name, description):
                success_count += 1
    
    print(f"\n基础依赖安装结果: {success_count}/{len(basic_packages)} 成功")
    return success_count == len(basic_packages)

def install_ml_dependencies():
    """安装机器学习依赖 (可选)"""
    print_header("安装机器学习依赖 (可选)")
    
    print("这些依赖用于高级模型功能，如果只测试基础功能可以跳过。")
    choice = input("是否安装机器学习依赖? (y/n): ").strip().lower()
    
    if choice != 'y':
        print("⏭️ 跳过机器学习依赖安装")
        return True
    
    ml_packages = [
        ('scikit-learn', 'sklearn', '机器学习基础库'),
        ('xgboost', 'xgboost', 'XGBoost梯度提升库'),
        ('lightgbm', 'lightgbm', 'LightGBM梯度提升库')
    ]
    
    success_count = 0
    for package_name, import_name, description in ml_packages:
        if check_package(import_name):
            print(f"✅ {package_name} 已安装")
            success_count += 1
        else:
            if install_package(package_name, description):
                success_count += 1
    
    print(f"\n机器学习依赖安装结果: {success_count}/{len(ml_packages)} 成功")
    return True  # 可选依赖，不影响基础功能

def install_tensorflow():
    """安装TensorFlow (可选)"""
    print_header("安装TensorFlow (可选)")
    
    print("TensorFlow用于LSTM模型，文件较大(~500MB)，下载时间较长。")
    print("如果只测试基础功能，可以跳过。")
    choice = input("是否安装TensorFlow? (y/n): ").strip().lower()
    
    if choice != 'y':
        print("⏭️ 跳过TensorFlow安装")
        return True
    
    if check_package('tensorflow'):
        print("✅ TensorFlow 已安装")
        return True
    else:
        print("⚠️ TensorFlow安装可能需要几分钟时间...")
        return install_package('tensorflow', 'LSTM深度学习模型库')

def verify_installation():
    """验证安装结果"""
    print_header("验证安装结果")
    
    packages_to_check = [
        ('pandas', 'pandas', '数据处理'),
        ('numpy', 'numpy', '数值计算'),
        ('yaml', 'yaml', '配置文件'),
        ('sklearn', 'scikit-learn', '机器学习'),
        ('xgboost', 'xgboost', 'XGBoost'),
        ('lightgbm', 'lightgbm', 'LightGBM'),
        ('tensorflow', 'tensorflow', 'TensorFlow')
    ]
    
    installed_count = 0
    for import_name, package_name, description in packages_to_check:
        if check_package(import_name):
            try:
                module = importlib.import_module(import_name)
                version = getattr(module, '__version__', '未知版本')
                print(f"✅ {package_name} ({description}): {version}")
                installed_count += 1
            except:
                print(f"✅ {package_name} ({description}): 已安装")
                installed_count += 1
        else:
            print(f"❌ {package_name} ({description}): 未安装")
    
    print(f"\n安装验证结果: {installed_count}/{len(packages_to_check)} 已安装")
    
    # 基础功能检查
    basic_packages = ['pandas', 'numpy', 'yaml']
    basic_installed = all(check_package(pkg) for pkg in basic_packages)
    
    if basic_installed:
        print("🎉 基础依赖已完整安装，可以运行P6-P7预测器基础功能！")
        return True
    else:
        print("⚠️ 基础依赖不完整，可能影响P6-P7预测器功能。")
        return False

def show_next_steps():
    """显示下一步操作"""
    print_header("下一步操作")
    
    print("依赖安装完成后，您可以:")
    print()
    print("1. 🧪 运行测试脚本验证环境:")
    print("   python test_personal_env.py")
    print()
    print("2. 📖 查看使用指南:")
    print("   查看文件: 个人开发环境测试指南.md")
    print()
    print("3. 🚀 开始使用P6-P7预测器:")
    print("   python scripts/train_sum_predictor.py --help")
    print("   python scripts/predict_span.py --help")
    print()
    print("4. 📚 查看详细文档:")
    print("   docs/P7_SPAN_PREDICTOR_README.md")

def main():
    """主函数"""
    print("📦 P6-P7预测器依赖安装工具")
    print("专为Python 3.11.9个人开发环境设计")
    print("不使用Anaconda或Docker，使用标准pip安装")
    
    # 检查Python版本
    version_info = sys.version_info
    print(f"\nPython版本: {version_info.major}.{version_info.minor}.{version_info.micro}")
    
    if version_info.major != 3 or version_info.minor != 11:
        print("⚠️ 建议使用Python 3.11.x版本以获得最佳兼容性")
    
    # 检查pip
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"pip版本: {result.stdout.strip()}")
        else:
            print("❌ pip不可用，请检查Python安装")
            return 1
    except:
        print("❌ pip检查失败，请检查Python安装")
        return 1
    
    print("\n开始安装依赖包...")
    
    # 安装步骤
    steps = [
        ("基础依赖", install_basic_dependencies),
        ("机器学习依赖", install_ml_dependencies),
        ("TensorFlow", install_tensorflow),
        ("验证安装", verify_installation)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
        except KeyboardInterrupt:
            print(f"\n⏹️ 用户中断了 {step_name} 安装")
            break
        except Exception as e:
            print(f"💥 {step_name} 安装异常: {e}")
    
    # 显示结果
    if success_count >= 2:  # 基础依赖和验证通过
        print("\n🎉 依赖安装基本完成！")
        show_next_steps()
        return 0
    else:
        print("\n⚠️ 依赖安装未完全成功，请检查错误信息并重试。")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ 安装被用户中断")
        sys.exit(1)

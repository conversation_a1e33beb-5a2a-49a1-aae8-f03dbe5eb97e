#!/usr/bin/env python3
"""
P6-P7预测器个人开发环境测试脚本

专为Python 3.11.9个人开发环境设计
不依赖Anaconda或Docker，使用标准pip安装的包

使用方法:
python test_personal_env.py
"""

import sys
import os
import sqlite3
import traceback
from pathlib import Path

def print_header(title):
    """打印测试标题"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print('='*60)

def print_result(test_name, success, message=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{status} {test_name}")
    if message:
        print(f"   {message}")

def test_python_environment():
    """测试1: Python环境验证"""
    print_header("测试1: Python环境验证")
    
    try:
        # 检查Python版本
        version = sys.version
        print(f"Python版本: {version}")
        
        # 检查是否是3.11.9
        version_info = sys.version_info
        if version_info.major == 3 and version_info.minor == 11:
            print_result("Python版本检查", True, f"Python {version_info.major}.{version_info.minor}.{version_info.micro}")
        else:
            print_result("Python版本检查", False, f"期望3.11.x，实际{version_info.major}.{version_info.minor}.{version_info.micro}")
        
        # 检查工作目录
        cwd = os.getcwd()
        print(f"当前目录: {cwd}")
        if "fucai3d" in cwd:
            print_result("工作目录检查", True, "在fucai3d项目目录中")
        else:
            print_result("工作目录检查", False, "请在fucai3d项目根目录运行")
        
        return True
    except Exception as e:
        print_result("Python环境验证", False, str(e))
        return False

def test_basic_imports():
    """测试2: 基础库导入"""
    print_header("测试2: 基础库导入")
    
    basic_modules = [
        ('sys', '系统模块'),
        ('os', '操作系统模块'),
        ('sqlite3', 'SQLite数据库'),
        ('json', 'JSON处理'),
        ('pathlib', '路径处理'),
        ('logging', '日志模块')
    ]
    
    success_count = 0
    for module_name, description in basic_modules:
        try:
            __import__(module_name)
            print_result(f"{module_name} ({description})", True)
            success_count += 1
        except ImportError as e:
            print_result(f"{module_name} ({description})", False, str(e))
    
    total_success = success_count == len(basic_modules)
    print_result("基础库导入", total_success, f"{success_count}/{len(basic_modules)} 成功")
    return total_success

def test_scientific_imports():
    """测试3: 科学计算库导入"""
    print_header("测试3: 科学计算库导入")
    
    scientific_modules = [
        ('pandas', 'pd', '数据处理'),
        ('numpy', 'np', '数值计算'),
        ('yaml', None, 'YAML配置')
    ]
    
    success_count = 0
    for module_info in scientific_modules:
        module_name = module_info[0]
        alias = module_info[1]
        description = module_info[2]
        
        try:
            if alias:
                exec(f"import {module_name} as {alias}")
                version = eval(f"{alias}.__version__")
                print_result(f"{module_name} ({description})", True, f"版本: {version}")
            else:
                __import__(module_name)
                print_result(f"{module_name} ({description})", True)
            success_count += 1
        except ImportError as e:
            print_result(f"{module_name} ({description})", False, f"未安装: {e}")
        except Exception as e:
            print_result(f"{module_name} ({description})", False, str(e))
    
    total_success = success_count >= 2  # pandas和numpy是必需的
    print_result("科学计算库导入", total_success, f"{success_count}/{len(scientific_modules)} 成功")
    return total_success

def test_database_connection():
    """测试4: 数据库连接"""
    print_header("测试4: 数据库连接")
    
    try:
        db_path = "data/lottery.db"
        
        # 检查文件存在
        if not Path(db_path).exists():
            print_result("数据库文件检查", False, f"文件不存在: {db_path}")
            return False
        
        print_result("数据库文件检查", True, f"文件存在: {db_path}")
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        print(f"数据库表: {table_names}")
        
        if 'lottery_records' in table_names:
            print_result("lottery_records表检查", True)
            
            # 检查数据量
            cursor.execute("SELECT COUNT(*) FROM lottery_records")
            count = cursor.fetchone()[0]
            print_result("数据量检查", count > 0, f"{count} 条记录")
            
            # 显示样本数据
            if count > 0:
                cursor.execute("SELECT period, numbers FROM lottery_records ORDER BY id DESC LIMIT 3")
                samples = cursor.fetchall()
                print("数据样本:")
                for period, numbers in samples:
                    print(f"  期号: {period}, 号码: {numbers}")
        else:
            print_result("lottery_records表检查", False, "表不存在")
            conn.close()
            return False
        
        conn.close()
        print_result("数据库连接", True, "连接和查询成功")
        return True
        
    except Exception as e:
        print_result("数据库连接", False, str(e))
        return False

def test_project_imports():
    """测试5: 项目代码导入"""
    print_header("测试5: 项目代码导入")
    
    # 添加项目路径
    sys.path.insert(0, str(Path.cwd()))
    
    project_modules = [
        ('src.data.sum_data_access', 'SumDataAccess', 'P6数据访问层'),
        ('src.data.span_data_access', 'SpanDataAccess', 'P7数据访问层'),
        ('src.predictors.sum_predictor', 'SumPredictor', 'P6预测器'),
        ('src.predictors.span_predictor', 'SpanPredictor', 'P7预测器')
    ]
    
    success_count = 0
    for module_name, class_name, description in project_modules:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print_result(f"{class_name} ({description})", True)
            success_count += 1
        except ImportError as e:
            print_result(f"{class_name} ({description})", False, f"导入错误: {e}")
        except AttributeError as e:
            print_result(f"{class_name} ({description})", False, f"类不存在: {e}")
        except Exception as e:
            print_result(f"{class_name} ({description})", False, str(e))
    
    total_success = success_count == len(project_modules)
    print_result("项目代码导入", total_success, f"{success_count}/{len(project_modules)} 成功")
    return total_success

def test_data_loading():
    """测试6: 数据加载功能"""
    print_header("测试6: 数据加载功能")
    
    try:
        # 测试P6数据加载
        from src.data.sum_data_access import SumDataAccess
        
        sum_data = SumDataAccess('data/lottery.db')
        df_sum = sum_data.load_lottery_data(limit=5)
        
        if not df_sum.empty:
            print_result("P6数据加载", True, f"加载 {len(df_sum)} 条记录")
            print("P6数据样本:")
            print(df_sum.head().to_string())
        else:
            print_result("P6数据加载", False, "返回空DataFrame")
            return False
        
        # 测试P7数据加载
        from src.data.span_data_access import SpanDataAccess
        
        span_data = SpanDataAccess('data/lottery.db')
        df_span = span_data.load_lottery_data(limit=5)
        
        if not df_span.empty:
            print_result("P7数据加载", True, f"加载 {len(df_span)} 条记录")
            print("P7数据样本:")
            print(df_span.head().to_string())
        else:
            print_result("P7数据加载", False, "返回空DataFrame")
            return False
        
        print_result("数据加载功能", True, "P6和P7数据加载都成功")
        return True
        
    except Exception as e:
        print_result("数据加载功能", False, str(e))
        traceback.print_exc()
        return False

def test_predictor_initialization():
    """测试7: 预测器初始化"""
    print_header("测试7: 预测器初始化")
    
    try:
        # 测试P6预测器
        from src.predictors.sum_predictor import SumPredictor
        
        sum_predictor = SumPredictor('data/lottery.db')
        print_result("P6预测器初始化", True, f"位置: {sum_predictor.position}")
        print(f"  预测范围: {sum_predictor.prediction_range}")
        print(f"  目标类型: {sum_predictor.target_type}")
        
        # 测试P7预测器
        from src.predictors.span_predictor import SpanPredictor
        
        span_predictor = SpanPredictor('data/lottery.db')
        print_result("P7预测器初始化", True, f"位置: {span_predictor.position}")
        print(f"  预测范围: {span_predictor.prediction_range}")
        print(f"  目标类型: {span_predictor.target_type}")
        
        # 测试P7专属功能
        span_predictor.enable_dual_constraints(True)
        span_predictor.enable_pattern_analysis(True)
        config = span_predictor.get_constraint_configuration()
        print_result("P7专属功能", True, f"约束配置: {config}")
        
        print_result("预测器初始化", True, "P6和P7预测器都初始化成功")
        return True
        
    except Exception as e:
        print_result("预测器初始化", False, str(e))
        traceback.print_exc()
        return False

def generate_test_report(results):
    """生成测试报告"""
    print_header("测试报告")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"测试环境: Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print(f"项目路径: {os.getcwd()}")
    print(f"测试时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    # 评估和建议
    print("\n🎯 评估和建议:")
    if passed_tests == total_tests:
        print("🎉 所有测试通过！P6-P7预测器在您的个人开发环境中完全可用。")
        print("📋 下一步建议:")
        print("  1. 尝试运行训练脚本: python scripts/train_sum_predictor.py --help")
        print("  2. 尝试运行预测脚本: python scripts/predict_span.py --help")
        print("  3. 查看详细文档: docs/P7_SPAN_PREDICTOR_README.md")
    elif passed_tests >= total_tests * 0.8:
        print("✅ 大部分测试通过，基本功能可用。")
        print("⚠️ 请解决失败的测试项目以获得完整功能。")
    elif passed_tests >= total_tests * 0.5:
        print("⚠️ 部分测试通过，需要解决一些问题。")
        print("💡 建议优先安装缺失的依赖包: pip install pandas numpy pyyaml")
    else:
        print("❌ 多数测试失败，需要检查环境配置。")
        print("🔧 建议:")
        print("  1. 确认在fucai3d项目根目录运行")
        print("  2. 安装必需依赖: pip install pandas numpy pyyaml")
        print("  3. 检查Python版本是否为3.11.x")

def main():
    """主函数"""
    print("🚀 P6-P7预测器个人开发环境测试")
    print("专为Python 3.11.9个人开发环境设计")
    print("不依赖Anaconda或Docker")
    
    # 执行所有测试
    tests = [
        ("Python环境验证", test_python_environment),
        ("基础库导入", test_basic_imports),
        ("科学计算库导入", test_scientific_imports),
        ("数据库连接", test_database_connection),
        ("项目代码导入", test_project_imports),
        ("数据加载功能", test_data_loading),
        ("预测器初始化", test_predictor_initialization)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成报告
    generate_test_report(results)
    
    # 返回退出码
    return 0 if all(results.values()) else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

"""
P7跨度预测器 - 分类预测模型（专属特征）

实现分类预测模型，10分类问题，预测跨度概率分布
支持Top-K预测，这是P7跨度预测器的专属特征

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    xgb = None

from .base_span_model import BaseSpanModel

class ClassificationSpanModel(BaseSpanModel):
    """分类跨度预测模型（专属特征）"""
    
    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化分类跨度模型
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        super().__init__(db_path, config_path)
        
        if not XGBOOST_AVAILABLE:
            raise ImportError("XGBoost未安装，请运行: pip install xgboost")
        
        self.model_type = 'classification'
        self.model = None
        self.feature_names = None
        self.is_trained = False
        
        # 分类专属属性
        self.num_classes = 10  # 跨度0-9
        self.class_labels = list(range(10))
        
        # 加载分类配置
        self.classification_config = self.config.get('span_predictor', {}).get('models', {}).get('classification', {})
        
        # 默认参数
        self.default_params = {
            'base_model': 'xgb',
            'n_estimators': 150,
            'max_depth': 5,
            'learning_rate': 0.1,
            'objective': 'multi:softprob',
            'num_class': 10,
            'eval_metric': 'mlogloss',
            'early_stopping_rounds': 15
        }
        
        # 合并配置参数
        self.params = {**self.default_params, **self.classification_config}
        
        # Top-K预测配置
        self.top_k = self.config.get('span_predictor', {}).get('span_config', {}).get('classification', {}).get('top_k_predictions', 3)
        
        self.logger.info(f"分类跨度模型初始化完成，参数: {self.params}")
    
    def build_model(self) -> bool:
        """
        构建分类模型
        
        Returns:
            是否构建成功
        """
        try:
            # 创建XGBoost分类器
            self.model = xgb.XGBClassifier(
                n_estimators=self.params['n_estimators'],
                max_depth=self.params['max_depth'],
                learning_rate=self.params['learning_rate'],
                objective=self.params['objective'],
                num_class=self.params['num_class'],
                eval_metric=self.params['eval_metric'],
                random_state=42,
                n_jobs=-1
            )
            
            self.logger.info("分类跨度模型构建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"分类跨度模型构建失败: {e}")
            return False
    
    def prepare_features(self, data: pd.DataFrame) -> np.ndarray:
        """
        准备分类特征数据（增强版特征工程）
        
        Args:
            data: 原始数据
            
        Returns:
            特征矩阵
        """
        features = []
        
        # 基础特征：历史跨度
        if 'span' in data.columns:
            features.append(data['span'].values.reshape(-1, 1))
        
        # 位置特征
        if all(col in data.columns for col in ['hundreds', 'tens', 'units']):
            position_features = data[['hundreds', 'tens', 'units']].values
            features.append(position_features)
        
        # 和值特征
        if all(col in data.columns for col in ['hundreds', 'tens', 'units']):
            sum_values = (data['hundreds'] + data['tens'] + data['units']).values.reshape(-1, 1)
            features.append(sum_values)
        
        # 分类专属特征：跨度分布特征
        if 'span' in data.columns:
            span_distribution_features = self._extract_span_distribution_features(data)
            features.append(span_distribution_features)
        
        # 模式特征（增强版）
        if all(col in data.columns for col in ['hundreds', 'tens', 'units']):
            pattern_features = self._extract_enhanced_pattern_features(data)
            features.append(pattern_features)
        
        # 频次特征
        if 'span' in data.columns:
            frequency_features = self._extract_frequency_features(data)
            features.append(frequency_features)
        
        # 滞后特征
        if 'span' in data.columns:
            lag_features = self._extract_lag_features(data)
            features.append(lag_features)
        
        # 合并所有特征
        if features:
            X = np.hstack(features)
            
            # 记录特征名称
            if self.feature_names is None:
                self.feature_names = self._generate_feature_names(X.shape[1])
            
            return X
        else:
            raise ValueError("无法提取有效特征")
    
    def _extract_span_distribution_features(self, data: pd.DataFrame) -> np.ndarray:
        """提取跨度分布特征（专属特征）"""
        distribution_features = []
        
        for i in range(len(data)):
            # 历史跨度分布
            if i >= 10:
                recent_spans = data['span'].iloc[i-10:i].values
                
                # 跨度分布统计
                span_counts = np.bincount(recent_spans.astype(int), minlength=10)
                span_probs = span_counts / np.sum(span_counts) if np.sum(span_counts) > 0 else np.ones(10) / 10
                
                # 分布熵
                entropy = -np.sum(span_probs * np.log(span_probs + 1e-8))
                
                # 最频繁跨度
                most_frequent_span = np.argmax(span_counts)
                
                # 分布方差
                span_variance = np.var(recent_spans)
                
                distribution_features.append(list(span_probs) + [entropy, most_frequent_span, span_variance])
            else:
                # 不足10个历史数据时使用默认值
                default_probs = np.ones(10) / 10
                distribution_features.append(list(default_probs) + [np.log(10), 5, 2.5])
        
        return np.array(distribution_features)
    
    def _extract_enhanced_pattern_features(self, data: pd.DataFrame) -> np.ndarray:
        """提取增强模式特征"""
        pattern_features = []
        
        for _, row in data.iterrows():
            digits = [row['hundreds'], row['tens'], row['units']]
            
            # 基础模式
            ascending = 1 if digits == sorted(digits) and len(set(digits)) == 3 else 0
            descending = 1 if digits == sorted(digits, reverse=True) and len(set(digits)) == 3 else 0
            same_digit = 1 if len(set(digits)) == 1 else 0
            consecutive = 1 if self._is_consecutive(digits) else 0
            
            # 增强模式特征
            has_duplicate = 1 if len(set(digits)) < 3 else 0
            all_even = 1 if all(d % 2 == 0 for d in digits) else 0
            all_odd = 1 if all(d % 2 == 1 for d in digits) else 0
            
            # 数字特征
            digit_sum = sum(digits)
            digit_product = digits[0] * digits[1] * digits[2]
            digit_variance = np.var(digits)
            
            # 位置关系特征
            h_t_diff = abs(digits[0] - digits[1])
            t_u_diff = abs(digits[1] - digits[2])
            h_u_diff = abs(digits[0] - digits[2])
            
            pattern_features.append([
                ascending, descending, same_digit, consecutive, has_duplicate,
                all_even, all_odd, digit_sum, digit_product, digit_variance,
                h_t_diff, t_u_diff, h_u_diff
            ])
        
        return np.array(pattern_features)
    
    def _extract_frequency_features(self, data: pd.DataFrame) -> np.ndarray:
        """提取频次特征"""
        frequency_features = []
        
        for i in range(len(data)):
            if i >= 20:
                # 最近20期的跨度频次
                recent_spans = data['span'].iloc[i-20:i].values
                span_counts = np.bincount(recent_spans.astype(int), minlength=10)
                
                # 当前跨度的历史频次
                current_span = data['span'].iloc[i]
                current_frequency = span_counts[int(current_span)] / 20
                
                # 热门跨度（出现次数最多的前3个）
                top_3_spans = np.argsort(span_counts)[-3:]
                is_in_top_3 = 1 if current_span in top_3_spans else 0
                
                # 冷门跨度（出现次数最少的前3个）
                bottom_3_spans = np.argsort(span_counts)[:3]
                is_in_bottom_3 = 1 if current_span in bottom_3_spans else 0
                
                frequency_features.append([current_frequency, is_in_top_3, is_in_bottom_3])
            else:
                frequency_features.append([0.1, 0, 0])  # 默认值
        
        return np.array(frequency_features)
    
    def _extract_lag_features(self, data: pd.DataFrame) -> np.ndarray:
        """提取滞后特征"""
        lag_features = []
        
        for lag in [1, 2, 3, 5]:
            lag_feature = data['span'].shift(lag).fillna(data['span'].mean()).values.reshape(-1, 1)
            lag_features.append(lag_feature)
        
        return np.hstack(lag_features)
    
    def _is_consecutive(self, digits: List[int]) -> bool:
        """判断是否为连续数字"""
        if len(set(digits)) != 3:
            return False
        sorted_digits = sorted(digits)
        return (sorted_digits[1] - sorted_digits[0] == 1 and 
                sorted_digits[2] - sorted_digits[1] == 1)
    
    def _generate_feature_names(self, num_features: int) -> List[str]:
        """生成特征名称"""
        names = []
        
        # 基础特征
        names.extend(['span', 'hundreds', 'tens', 'units', 'sum'])
        
        # 跨度分布特征
        names.extend([f'span_prob_{i}' for i in range(10)])
        names.extend(['span_entropy', 'most_frequent_span', 'span_variance'])
        
        # 增强模式特征
        names.extend([
            'ascending', 'descending', 'same_digit', 'consecutive', 'has_duplicate',
            'all_even', 'all_odd', 'digit_sum', 'digit_product', 'digit_variance',
            'h_t_diff', 't_u_diff', 'h_u_diff'
        ])
        
        # 频次特征
        names.extend(['current_frequency', 'is_in_top_3', 'is_in_bottom_3'])
        
        # 滞后特征
        names.extend([f'span_lag_{lag}' for lag in [1, 2, 3, 5]])
        
        # 补充通用名称
        while len(names) < num_features:
            names.append(f'feature_{len(names)}')
        
        return names[:num_features]
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载训练数据
        
        Returns:
            特征矩阵和目标向量
        """
        try:
            # 从数据库加载数据
            data = self.data_access.load_lottery_data()
            
            if data.empty:
                raise ValueError("数据库中没有数据")
            
            # 计算跨度
            data['span'] = data[['hundreds', 'tens', 'units']].max(axis=1) - data[['hundreds', 'tens', 'units']].min(axis=1)
            
            # 准备特征
            X = self.prepare_features(data)
            
            # 目标变量（跨度类别）
            y = data['span'].values.astype(int)
            
            # 确保数据长度一致
            min_length = min(len(X), len(y))
            X = X[:min_length]
            y = y[:min_length]
            
            self.logger.info(f"加载分类跨度数据: {len(X)} 个样本, {X.shape[1]} 个特征, {len(np.unique(y))} 个类别")
            return X, y
            
        except Exception as e:
            self.logger.error(f"加载分类跨度数据失败: {e}")
            raise
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X: 特征矩阵（可选）
            y: 目标向量（可选）
            
        Returns:
            训练性能指标
        """
        try:
            # 如果没有提供数据，从数据库加载
            if X is None or y is None:
                X, y = self.load_data()
            
            # 构建模型
            if self.model is None:
                self.build_model()
            
            # 数据分割
            from sklearn.model_selection import train_test_split
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 训练模型
            eval_set = [(X_val, y_val)]
            
            self.model.fit(
                X_train, y_train,
                eval_set=eval_set,
                early_stopping_rounds=self.params.get('early_stopping_rounds', 15),
                verbose=False
            )
            
            self.is_trained = True
            
            # 评估性能
            performance = self.evaluate(X_val, y_val)
            
            # 添加分类专属指标
            classification_metrics = self.evaluate_classification(X_val, y_val)
            performance.update(classification_metrics)
            
            self.logger.info(f"分类跨度模型训练完成: {performance}")
            return performance
            
        except Exception as e:
            self.logger.error(f"分类跨度模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测跨度类别
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果
        """
        if not self.is_trained or self.model is None:
            raise ValueError("模型尚未训练")
        
        try:
            predictions = self.model.predict(X)
            return predictions.astype(float)
            
        except Exception as e:
            self.logger.error(f"分类跨度预测失败: {e}")
            raise
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """
        预测跨度概率分布
        
        Args:
            X: 特征矩阵
            
        Returns:
            概率分布矩阵
        """
        if not self.is_trained or self.model is None:
            raise ValueError("模型尚未训练")
        
        try:
            probabilities = self.model.predict_proba(X)
            return probabilities
            
        except Exception as e:
            self.logger.error(f"分类跨度概率预测失败: {e}")
            raise
    
    def predict_top_k(self, X: np.ndarray, k: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Top-K预测（专属特征）
        
        Args:
            X: 特征矩阵
            k: Top-K数量
            
        Returns:
            Top-K类别和对应概率
        """
        if k is None:
            k = self.top_k
        
        probabilities = self.predict_proba(X)
        
        # 获取Top-K
        top_k_indices = np.argsort(probabilities, axis=1)[:, -k:][:, ::-1]
        top_k_probs = np.array([probabilities[i, indices] for i, indices in enumerate(top_k_indices)])
        
        return top_k_indices, top_k_probs
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        带置信度的预测
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果和置信度
        """
        probabilities = self.predict_proba(X)
        predictions = np.argmax(probabilities, axis=1).astype(float)
        
        # 置信度为最大概率值
        confidences = np.max(probabilities, axis=1)
        
        return predictions, confidences
    
    def evaluate_classification(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """
        评估分类性能
        
        Args:
            X_test: 测试特征
            y_test: 测试目标
            
        Returns:
            分类性能指标
        """
        try:
            from sklearn.metrics import accuracy_score, f1_score
            
            predictions = self.predict(X_test)
            probabilities = self.predict_proba(X_test)
            
            # 分类准确率
            classification_accuracy = accuracy_score(y_test, predictions)
            
            # F1分数
            f1 = f1_score(y_test, predictions, average='weighted')
            
            # Top-3准确率
            top_3_indices, _ = self.predict_top_k(X_test, k=3)
            top_3_accuracy = np.mean([y_test[i] in top_3_indices[i] for i in range(len(y_test))])
            
            return {
                'classification_accuracy': float(classification_accuracy),
                'f1_score': float(f1),
                'top_3_accuracy': float(top_3_accuracy)
            }
            
        except Exception as e:
            self.logger.error(f"分类性能评估失败: {e}")
            return {
                'classification_accuracy': 0.0,
                'f1_score': 0.0,
                'top_3_accuracy': 0.0
            }
    
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            是否保存成功
        """
        if not self.is_trained or self.model is None:
            self.logger.warning("模型尚未训练，无法保存")
            return False
        
        try:
            # 确保目录存在
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存模型和元数据
            model_data = {
                'model': self.model,
                'feature_names': self.feature_names,
                'params': self.params,
                'model_type': self.model_type,
                'is_trained': self.is_trained,
                'num_classes': self.num_classes,
                'class_labels': self.class_labels,
                'top_k': self.top_k
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"分类跨度模型已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存分类跨度模型失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            if not Path(filepath).exists():
                self.logger.error(f"模型文件不存在: {filepath}")
                return False
            
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.feature_names = model_data.get('feature_names')
            self.params = model_data.get('params', self.params)
            self.is_trained = model_data.get('is_trained', True)
            self.num_classes = model_data.get('num_classes', 10)
            self.class_labels = model_data.get('class_labels', list(range(10)))
            self.top_k = model_data.get('top_k', 3)
            
            self.logger.info(f"分类跨度模型已从 {filepath} 加载")
            return True
            
        except Exception as e:
            self.logger.error(f"加载分类跨度模型失败: {e}")
            return False

#!/usr/bin/env python3
"""
P6-P7预测器快速演示脚本

展示P6和值预测器和P7跨度预测器的基础功能
适用于个人Python 3.11.9开发环境

使用方法:
python quick_demo.py
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path.cwd()))

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print('='*60)

def demo_data_access():
    """演示数据访问功能"""
    print_header("数据访问演示")
    
    try:
        from src.data.sum_data_access import SumDataAccess
        from src.data.span_data_access import SpanDataAccess
        
        print("📊 P6和值数据访问演示:")
        sum_data = SumDataAccess('data/lottery.db')
        df_sum = sum_data.load_lottery_data(limit=5)
        print(f"✅ 加载了 {len(df_sum)} 条和值数据")
        print("数据样本:")
        print(df_sum[['issue', 'hundreds', 'tens', 'units', 'sum']].to_string())
        
        print("\n📊 P7跨度数据访问演示:")
        span_data = SpanDataAccess('data/lottery.db')
        df_span = span_data.load_lottery_data(limit=5)
        print(f"✅ 加载了 {len(df_span)} 条跨度数据")
        print("数据样本:")
        print(df_span[['issue', 'hundreds', 'tens', 'units', 'span']].to_string())
        
        return True
        
    except Exception as e:
        print(f"❌ 数据访问演示失败: {e}")
        return False

def demo_sum_predictor():
    """演示P6和值预测器"""
    print_header("P6和值预测器演示")
    
    try:
        from src.predictors.sum_predictor import SumPredictor
        
        # 初始化预测器
        predictor = SumPredictor('data/lottery.db')
        print(f"✅ P6和值预测器初始化成功")
        print(f"   位置: {predictor.position}")
        print(f"   预测范围: {predictor.prediction_range}")
        print(f"   目标类型: {predictor.target_type}")
        
        # 获取模型信息
        info = predictor.get_model_info()
        print(f"   模型信息: {info}")
        
        # 加载训练数据
        X, y = predictor.load_training_data()
        print(f"✅ 训练数据加载成功: {len(X)} 个样本, {X.shape[1]} 个特征")
        print(f"   和值范围: {y.min()} - {y.max()}")
        
        # 显示数据统计
        print("📈 和值分布统计:")
        import pandas as pd
        value_counts = pd.Series(y).value_counts().sort_index()
        for sum_val, count in value_counts.head(10).items():
            print(f"   和值{sum_val}: {count}次")
        
        return True
        
    except Exception as e:
        print(f"❌ P6和值预测器演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_span_predictor():
    """演示P7跨度预测器"""
    print_header("P7跨度预测器演示")
    
    try:
        from src.predictors.span_predictor import SpanPredictor
        
        # 初始化预测器
        predictor = SpanPredictor('data/lottery.db')
        print(f"✅ P7跨度预测器初始化成功")
        print(f"   位置: {predictor.position}")
        print(f"   预测范围: {predictor.prediction_range}")
        print(f"   目标类型: {predictor.target_type}")
        
        # 测试专属功能
        print("\n🌟 P7专属功能演示:")
        
        # 启用双重约束
        predictor.enable_dual_constraints(True)
        print("✅ 双重约束优化已启用")
        
        # 启用模式分析
        predictor.enable_pattern_analysis(True)
        print("✅ 模式分析功能已启用")
        
        # 获取约束配置
        config = predictor.get_constraint_configuration()
        print(f"   约束配置: {config}")
        
        # 模式分析演示
        print("\n🔍 模式分析演示:")
        test_patterns = [
            [1, 2, 3],  # 升序
            [3, 2, 1],  # 降序
            [5, 5, 5],  # 相同数字
            [1, 3, 5],  # 跳跃
        ]
        
        for pattern in test_patterns:
            result = predictor.predict_pattern_probability(pattern)
            print(f"   {pattern} -> 跨度:{result['predicted_span']}, "
                  f"升序:{result['ascending']:.2f}, "
                  f"相同:{result['same_digit']:.2f}")
        
        # 加载训练数据
        X, y = predictor.load_training_data()
        print(f"\n✅ 训练数据加载成功: {len(X)} 个样本, {X.shape[1]} 个特征")
        print(f"   跨度范围: {y.min()} - {y.max()}")
        
        # 显示跨度分布
        print("📈 跨度分布统计:")
        import pandas as pd
        value_counts = pd.Series(y).value_counts().sort_index()
        for span_val, count in value_counts.items():
            print(f"   跨度{span_val}: {count}次 ({count/len(y)*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ P7跨度预测器演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_constraint_analysis():
    """演示约束分析功能"""
    print_header("约束分析演示")
    
    try:
        from src.predictors.span_predictor import SpanPredictor
        
        predictor = SpanPredictor('data/lottery.db')
        predictor.enable_dual_constraints(True)
        
        # 模拟预测结果
        position_predictions = {'hundreds': 1, 'tens': 2, 'units': 3}
        sum_prediction = 6.0
        span_prediction = 2.0
        
        print("🔗 约束一致性分析演示:")
        print(f"   位置预测: {position_predictions}")
        print(f"   和值预测: {sum_prediction}")
        print(f"   跨度预测: {span_prediction}")
        
        # 计算约束一致性
        scores = predictor.calculate_constraint_consistency_score(
            span_prediction, position_predictions, sum_prediction
        )
        
        print("📊 约束一致性评分:")
        for key, score in scores.items():
            if isinstance(score, (int, float)):
                print(f"   {key}: {score:.3f}")
            else:
                print(f"   {key}: {score}")
        
        return True
        
    except Exception as e:
        print(f"❌ 约束分析演示失败: {e}")
        return False

def demo_configuration():
    """演示配置文件功能"""
    print_header("配置文件演示")
    
    try:
        import yaml
        
        # 读取P6配置
        with open('config/sum_predictor_config.yaml', 'r', encoding='utf-8') as f:
            sum_config = yaml.safe_load(f)
        
        print("📄 P6和值预测器配置:")
        print(f"   模型配置: {list(sum_config.get('sum_predictor', {}).get('models', {}).keys())}")
        
        # 读取P7配置
        with open('config/span_predictor_config.yaml', 'r', encoding='utf-8') as f:
            span_config = yaml.safe_load(f)
        
        print("📄 P7跨度预测器配置:")
        span_cfg = span_config.get('span_predictor', {})
        print(f"   模型配置: {list(span_cfg.get('models', {}).keys())}")
        print(f"   专属配置: {list(span_cfg.get('span_config', {}).keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件演示失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print_header("使用示例")
    
    print("🚀 P6-P7预测器使用示例:")
    print()
    print("1. 训练P6和值预测器:")
    print("   python scripts/train_sum_predictor.py --db-path data/lottery.db")
    print()
    print("2. 训练P7跨度预测器:")
    print("   python scripts/train_span_predictor.py --db-path data/lottery.db")
    print()
    print("3. 执行和值预测:")
    print("   python scripts/predict_sum.py --db-path data/lottery.db --issue 2025001")
    print()
    print("4. 执行跨度预测 (带约束):")
    print("   python scripts/predict_span.py --db-path data/lottery.db --issue 2025001 --enable-constraints")
    print()
    print("5. 使用P7命令行工具:")
    print("   python scripts/span_predictor_cli.py info --db-path data/lottery.db")
    print()
    print("6. 查看详细文档:")
    print("   查看文件: docs/P7_SPAN_PREDICTOR_README.md")

def main():
    """主函数"""
    print("🎯 P6-P7预测器快速演示")
    print("展示和值预测器和跨度预测器的核心功能")
    print("适用于Python 3.11.9个人开发环境")
    
    # 检查环境
    print(f"\nPython版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 演示步骤
    demos = [
        ("数据访问功能", demo_data_access),
        ("P6和值预测器", demo_sum_predictor),
        ("P7跨度预测器", demo_span_predictor),
        ("约束分析功能", demo_constraint_analysis),
        ("配置文件功能", demo_configuration)
    ]
    
    success_count = 0
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                success_count += 1
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"💥 {demo_name} 演示异常: {e}")
    
    # 显示结果
    print_header("演示结果")
    print(f"成功演示: {success_count}/{len(demos)}")
    
    if success_count == len(demos):
        print("🎉 所有功能演示成功！P6-P7预测器在您的环境中完全可用。")
    elif success_count >= len(demos) * 0.8:
        print("✅ 大部分功能演示成功，基本功能可用。")
    else:
        print("⚠️ 部分功能演示失败，请检查环境配置。")
        print("💡 建议先运行: python test_personal_env.py")
    
    # 显示使用示例
    show_usage_examples()
    
    return 0 if success_count >= len(demos) * 0.8 else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ 演示被用户中断")
        sys.exit(1)

#!/usr/bin/env python3
"""
P7跨度预测器训练脚本

支持所有6种模型的训练，包括命令行参数和进度显示
支持双重约束优化和模式分析功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.span_predictor import SpanPredictor

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """设置日志配置"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    handlers = [logging.StreamHandler(sys.stdout)]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=handlers
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="P7跨度预测器训练脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 训练所有模型
  python train_span_predictor.py --db-path data/lottery.db
  
  # 训练指定模型
  python train_span_predictor.py --db-path data/lottery.db --models xgb lgb
  
  # 启用双重约束
  python train_span_predictor.py --db-path data/lottery.db --enable-constraints
  
  # 保存模型
  python train_span_predictor.py --db-path data/lottery.db --save-models --model-dir models/span
        """
    )
    
    # 必需参数
    parser.add_argument(
        "--db-path",
        type=str,
        required=True,
        help="数据库文件路径"
    )
    
    # 可选参数
    parser.add_argument(
        "--config-path",
        type=str,
        default="config/span_predictor_config.yaml",
        help="配置文件路径 (默认: config/span_predictor_config.yaml)"
    )
    
    parser.add_argument(
        "--models",
        nargs="+",
        choices=["xgb", "lgb", "lstm", "classification", "constraint", "ensemble"],
        default=["xgb", "lgb", "lstm", "classification", "constraint", "ensemble"],
        help="要训练的模型列表 (默认: 所有模型)"
    )
    
    parser.add_argument(
        "--enable-constraints",
        action="store_true",
        help="启用双重约束优化"
    )
    
    parser.add_argument(
        "--enable-patterns",
        action="store_true",
        help="启用模式分析"
    )
    
    parser.add_argument(
        "--save-models",
        action="store_true",
        help="训练完成后保存模型"
    )
    
    parser.add_argument(
        "--model-dir",
        type=str,
        default="models/span_predictor",
        help="模型保存目录 (默认: models/span_predictor)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    
    parser.add_argument(
        "--log-file",
        type=str,
        help="日志文件路径"
    )
    
    parser.add_argument(
        "--data-limit",
        type=int,
        help="训练数据限制（用于测试）"
    )
    
    return parser.parse_args()

def validate_arguments(args):
    """验证命令行参数"""
    # 检查数据库文件
    if not Path(args.db_path).exists():
        raise FileNotFoundError(f"数据库文件不存在: {args.db_path}")
    
    # 检查配置文件
    if args.config_path and not Path(args.config_path).exists():
        logging.warning(f"配置文件不存在: {args.config_path}")
    
    # 创建模型目录
    if args.save_models:
        Path(args.model_dir).mkdir(parents=True, exist_ok=True)

def print_training_header(args):
    """打印训练头部信息"""
    print("=" * 80)
    print("P7跨度预测器训练脚本")
    print("=" * 80)
    print(f"数据库路径: {args.db_path}")
    print(f"配置文件: {args.config_path}")
    print(f"训练模型: {', '.join(args.models)}")
    print(f"双重约束: {'启用' if args.enable_constraints else '禁用'}")
    print(f"模式分析: {'启用' if args.enable_patterns else '禁用'}")
    print(f"保存模型: {'是' if args.save_models else '否'}")
    if args.save_models:
        print(f"模型目录: {args.model_dir}")
    print("=" * 80)

def train_models(predictor: SpanPredictor, models_to_train: list, args) -> Dict[str, Any]:
    """训练指定的模型"""
    training_results = {}
    
    print(f"\n开始训练 {len(models_to_train)} 个模型...")
    
    for i, model_name in enumerate(models_to_train, 1):
        print(f"\n[{i}/{len(models_to_train)}] 训练模型: {model_name}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # 切换到当前模型
            predictor.switch_model(model_name)
            
            # 加载训练数据
            print("加载训练数据...")
            X, y = predictor.load_training_data(limit=args.data_limit)
            print(f"训练数据: {len(X)} 个样本, {X.shape[1]} 个特征")
            
            # 训练模型
            print("开始训练...")
            if model_name in ['constraint', 'ensemble']:
                # 这些模型需要其他模型先训练完成
                result = predictor.models[model_name].train(X, y)
            else:
                result = predictor.models[model_name].train(X, y)
            
            training_time = time.time() - start_time
            result['training_time'] = training_time
            
            training_results[model_name] = result
            
            print(f"训练完成! 用时: {training_time:.2f}秒")
            
            # 显示训练结果
            if 'accuracy' in result:
                print(f"准确率: {result['accuracy']:.4f}")
            if 'mae' in result:
                print(f"平均绝对误差: {result['mae']:.4f}")
            
        except Exception as e:
            error_msg = f"训练失败: {str(e)}"
            print(f"错误: {error_msg}")
            logging.error(f"训练模型 {model_name} 失败: {e}")
            training_results[model_name] = {'error': error_msg}
    
    return training_results

def save_trained_models(predictor: SpanPredictor, model_dir: str, models_to_save: list) -> Dict[str, str]:
    """保存训练好的模型"""
    print(f"\n保存模型到: {model_dir}")
    print("-" * 40)
    
    saved_paths = {}
    
    for model_name in models_to_save:
        if model_name in predictor.models and predictor.models[model_name].is_trained:
            try:
                filepath = Path(model_dir) / f"{model_name}_span_model.pkl"
                success = predictor.models[model_name].save_model(str(filepath))
                
                if success:
                    saved_paths[model_name] = str(filepath)
                    print(f"✓ {model_name}: {filepath}")
                else:
                    print(f"✗ {model_name}: 保存失败")
                    
            except Exception as e:
                print(f"✗ {model_name}: {str(e)}")
                logging.error(f"保存模型 {model_name} 失败: {e}")
        else:
            print(f"- {model_name}: 未训练，跳过保存")
    
    return saved_paths

def print_training_summary(training_results: Dict[str, Any], saved_paths: Dict[str, str]):
    """打印训练摘要"""
    print("\n" + "=" * 80)
    print("训练摘要")
    print("=" * 80)
    
    successful_models = [name for name, result in training_results.items() if 'error' not in result]
    failed_models = [name for name, result in training_results.items() if 'error' in result]
    
    print(f"成功训练: {len(successful_models)} 个模型")
    for model_name in successful_models:
        result = training_results[model_name]
        training_time = result.get('training_time', 0)
        accuracy = result.get('accuracy', 0)
        mae = result.get('mae', 0)
        print(f"  ✓ {model_name}: 准确率={accuracy:.4f}, MAE={mae:.4f}, 用时={training_time:.2f}s")
    
    if failed_models:
        print(f"\n训练失败: {len(failed_models)} 个模型")
        for model_name in failed_models:
            error = training_results[model_name].get('error', '未知错误')
            print(f"  ✗ {model_name}: {error}")
    
    if saved_paths:
        print(f"\n已保存模型: {len(saved_paths)} 个")
        for model_name, path in saved_paths.items():
            print(f"  📁 {model_name}: {path}")
    
    print("=" * 80)

def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志
        setup_logging(args.log_level, args.log_file)
        
        # 验证参数
        validate_arguments(args)
        
        # 打印训练信息
        print_training_header(args)
        
        # 初始化预测器
        print("\n初始化跨度预测器...")
        predictor = SpanPredictor(args.db_path, args.config_path)
        
        # 配置预测器
        if args.enable_constraints:
            predictor.enable_dual_constraints(True)
        if args.enable_patterns:
            predictor.enable_pattern_analysis(True)
        
        # 构建模型
        print("构建模型...")
        if not predictor.build_model():
            raise RuntimeError("模型构建失败")
        
        # 训练模型
        training_results = train_models(predictor, args.models, args)
        
        # 保存模型
        saved_paths = {}
        if args.save_models:
            saved_paths = save_trained_models(predictor, args.model_dir, args.models)
        
        # 打印摘要
        print_training_summary(training_results, saved_paths)
        
        # 检查是否有训练成功的模型
        successful_count = len([r for r in training_results.values() if 'error' not in r])
        if successful_count == 0:
            print("\n⚠️  警告: 没有模型训练成功!")
            return 1
        else:
            print(f"\n🎉 训练完成! 成功训练 {successful_count} 个模型")
            return 0
            
    except KeyboardInterrupt:
        print("\n\n⚠️  训练被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 训练失败: {str(e)}")
        logging.error(f"训练脚本执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
P7跨度预测器评估脚本

支持模型性能评估、约束有效性评估和模式分析评估
提供详细的评估报告和可视化图表

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import logging
import json
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
import numpy as np
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.span_predictor import SpanPredictor

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="P7跨度预测器评估脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 评估所有模型
  python evaluate_span_predictor.py --db-path data/lottery.db
  
  # 评估指定模型
  python evaluate_span_predictor.py --db-path data/lottery.db --models xgb lgb
  
  # 评估约束有效性
  python evaluate_span_predictor.py --db-path data/lottery.db --evaluate-constraints
  
  # 评估模式分析
  python evaluate_span_predictor.py --db-path data/lottery.db --evaluate-patterns
  
  # 生成详细报告
  python evaluate_span_predictor.py --db-path data/lottery.db --detailed --output report.json
        """
    )
    
    # 必需参数
    parser.add_argument(
        "--db-path",
        type=str,
        required=True,
        help="数据库文件路径"
    )
    
    # 模型选择
    parser.add_argument(
        "--models",
        nargs="+",
        choices=["xgb", "lgb", "lstm", "classification", "constraint", "ensemble"],
        default=["xgb", "lgb", "lstm", "classification", "constraint", "ensemble"],
        help="要评估的模型列表 (默认: 所有模型)"
    )
    
    parser.add_argument(
        "--model-dir",
        type=str,
        default="models/span_predictor",
        help="模型文件目录 (默认: models/span_predictor)"
    )
    
    # 评估选项
    parser.add_argument(
        "--test-size",
        type=float,
        default=0.2,
        help="测试集比例 (默认: 0.2)"
    )
    
    parser.add_argument(
        "--evaluate-constraints",
        action="store_true",
        help="评估约束优化有效性"
    )
    
    parser.add_argument(
        "--evaluate-patterns",
        action="store_true",
        help="评估模式分析功能"
    )
    
    parser.add_argument(
        "--cross-validation",
        action="store_true",
        help="执行交叉验证"
    )
    
    parser.add_argument(
        "--cv-folds",
        type=int,
        default=5,
        help="交叉验证折数 (默认: 5)"
    )
    
    # 输出选项
    parser.add_argument(
        "--output",
        type=str,
        help="输出文件路径 (JSON格式)"
    )
    
    parser.add_argument(
        "--detailed",
        action="store_true",
        help="生成详细评估报告"
    )
    
    parser.add_argument(
        "--plot",
        action="store_true",
        help="生成评估图表"
    )
    
    parser.add_argument(
        "--plot-dir",
        type=str,
        default="evaluation_plots",
        help="图表保存目录 (默认: evaluation_plots)"
    )
    
    parser.add_argument(
        "--config-path",
        type=str,
        default="config/span_predictor_config.yaml",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    
    return parser.parse_args()

def validate_arguments(args):
    """验证命令行参数"""
    # 检查数据库文件
    if not Path(args.db_path).exists():
        raise FileNotFoundError(f"数据库文件不存在: {args.db_path}")
    
    # 检查模型目录
    if not Path(args.model_dir).exists():
        raise FileNotFoundError(f"模型目录不存在: {args.model_dir}")
    
    # 创建图表目录
    if args.plot:
        Path(args.plot_dir).mkdir(parents=True, exist_ok=True)

def load_predictor_models(predictor: SpanPredictor, model_dir: str, models_to_load: List[str]) -> Dict[str, bool]:
    """加载预测器模型"""
    print("加载模型...")
    
    load_results = {}
    for model_name in models_to_load:
        model_file = Path(model_dir) / f"{model_name}_span_model.pkl"
        
        if model_file.exists():
            try:
                success = predictor.load_model(model_name, str(model_file))
                load_results[model_name] = success
                status = "✓" if success else "✗"
                print(f"  {status} {model_name}")
            except Exception as e:
                load_results[model_name] = False
                print(f"  ✗ {model_name}: {str(e)}")
        else:
            load_results[model_name] = False
            print(f"  - {model_name}: 文件不存在")
    
    return load_results

def evaluate_model_performance(predictor: SpanPredictor, models_to_evaluate: List[str], test_size: float) -> Dict[str, Any]:
    """评估模型性能"""
    print("\n评估模型性能...")
    print("=" * 50)
    
    performance_results = {}
    
    # 加载数据
    X, y = predictor.load_training_data()
    
    # 分割数据
    from sklearn.model_selection import train_test_split
    _, X_test, _, y_test = train_test_split(X, y, test_size=test_size, random_state=42)
    
    print(f"测试数据: {len(X_test)} 个样本")
    
    for model_name in models_to_evaluate:
        if model_name in predictor.models and predictor.models[model_name].is_trained:
            print(f"\n评估模型: {model_name}")
            print("-" * 30)
            
            try:
                start_time = time.time()
                
                # 基础评估
                evaluation = predictor.evaluate_model(model_name, test_size)
                
                # 添加额外指标
                model = predictor.models[model_name]
                predictions = model.predict(X_test)
                
                # 计算详细指标
                mae = float(np.mean(np.abs(y_test - predictions)))
                rmse = float(np.sqrt(np.mean((y_test - predictions) ** 2)))
                accuracy_0 = float(np.mean(np.abs(y_test - predictions) <= 0.5))
                accuracy_1 = float(np.mean(np.abs(y_test - predictions) <= 1))
                accuracy_2 = float(np.mean(np.abs(y_test - predictions) <= 2))
                
                # R²分数
                ss_res = np.sum((y_test - predictions) ** 2)
                ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
                r2_score = float(1 - (ss_res / ss_tot)) if ss_tot != 0 else 0.0
                
                evaluation_time = time.time() - start_time
                
                detailed_metrics = {
                    'mae': mae,
                    'rmse': rmse,
                    'accuracy_0.5': accuracy_0,
                    'accuracy_1': accuracy_1,
                    'accuracy_2': accuracy_2,
                    'r2_score': r2_score,
                    'evaluation_time': evaluation_time,
                    'test_samples': len(y_test)
                }
                
                evaluation.update(detailed_metrics)
                performance_results[model_name] = evaluation
                
                # 显示结果
                print(f"MAE: {mae:.4f}")
                print(f"RMSE: {rmse:.4f}")
                print(f"±0.5准确率: {accuracy_0:.4f}")
                print(f"±1准确率: {accuracy_1:.4f}")
                print(f"±2准确率: {accuracy_2:.4f}")
                print(f"R²分数: {r2_score:.4f}")
                print(f"评估用时: {evaluation_time:.3f}秒")
                
            except Exception as e:
                print(f"评估失败: {str(e)}")
                performance_results[model_name] = {'error': str(e)}
        else:
            print(f"跳过模型 {model_name}: 未训练或不存在")
            performance_results[model_name] = {'status': 'not_available'}
    
    return performance_results

def evaluate_constraint_effectiveness(predictor: SpanPredictor, test_size: float) -> Dict[str, Any]:
    """评估约束优化有效性"""
    print("\n评估约束优化有效性...")
    print("=" * 50)
    
    try:
        # 启用约束优化
        predictor.enable_dual_constraints(True)
        
        # 评估约束有效性
        constraint_evaluation = predictor.evaluate_constraint_effectiveness(test_size)
        
        print("约束优化评估结果:")
        print("-" * 30)
        
        base_perf = constraint_evaluation.get('base_performance', {})
        constraint_perf = constraint_evaluation.get('constraint_performance', {})
        improvement = constraint_evaluation.get('improvement', {})
        
        print(f"基础性能:")
        print(f"  MAE: {base_perf.get('mae', 0):.4f}")
        print(f"  准确率: {base_perf.get('accuracy', 0):.4f}")
        
        print(f"约束优化性能:")
        print(f"  MAE: {constraint_perf.get('mae', 0):.4f}")
        print(f"  准确率: {constraint_perf.get('accuracy', 0):.4f}")
        
        print(f"改进情况:")
        print(f"  MAE改进: {improvement.get('mae_improvement', 0):.4f}")
        print(f"  准确率改进: {improvement.get('accuracy_improvement', 0):.4f}")
        print(f"  MAE改进百分比: {improvement.get('mae_improvement_percent', 0):.2f}%")
        print(f"  准确率改进百分比: {improvement.get('accuracy_improvement_percent', 0):.2f}%")
        
        return constraint_evaluation
        
    except Exception as e:
        print(f"约束评估失败: {str(e)}")
        return {'error': str(e)}

def evaluate_pattern_analysis(predictor: SpanPredictor) -> Dict[str, Any]:
    """评估模式分析功能"""
    print("\n评估模式分析功能...")
    print("=" * 50)
    
    try:
        # 启用模式分析
        predictor.enable_pattern_analysis(True)
        
        # 执行全面模式分析
        pattern_analysis = predictor.analyze_comprehensive_patterns("evaluation", 100)
        
        if 'error' in pattern_analysis:
            print(f"模式分析失败: {pattern_analysis['error']}")
            return pattern_analysis
        
        print("模式分析结果:")
        print("-" * 30)
        
        # 基础统计
        basic_stats = pattern_analysis.get('basic_statistics', {})
        print(f"跨度统计:")
        print(f"  平均值: {basic_stats.get('mean', 0):.2f}")
        print(f"  标准差: {basic_stats.get('std', 0):.2f}")
        print(f"  最小值: {basic_stats.get('min', 0)}")
        print(f"  最大值: {basic_stats.get('max', 0)}")
        print(f"  众数: {basic_stats.get('mode', 0)}")
        print(f"  熵: {basic_stats.get('entropy', 0):.3f}")
        
        # 模式分析
        pattern_stats = pattern_analysis.get('pattern_analysis', {})
        print(f"\n模式统计:")
        for pattern_name, pattern_info in pattern_stats.items():
            if isinstance(pattern_info, dict):
                count = pattern_info.get('count', 0)
                probability = pattern_info.get('probability', 0)
                avg_span = pattern_info.get('avg_span', 0)
                print(f"  {pattern_name}: 次数={count}, 概率={probability:.3f}, 平均跨度={avg_span:.2f}")
        
        # 趋势分析
        trend_analysis = pattern_analysis.get('trend_analysis', {})
        if 'error' not in trend_analysis:
            print(f"\n趋势分析:")
            print(f"  最近趋势: {trend_analysis.get('recent_trend', 'unknown')}")
            print(f"  波动性: {trend_analysis.get('volatility', 0):.3f}")
            print(f"  趋势强度: {trend_analysis.get('trend_strength', 0):.3f}")
        
        return pattern_analysis
        
    except Exception as e:
        print(f"模式分析评估失败: {str(e)}")
        return {'error': str(e)}

def perform_cross_validation(predictor: SpanPredictor, models_to_evaluate: List[str], cv_folds: int) -> Dict[str, Any]:
    """执行交叉验证"""
    print(f"\n执行 {cv_folds} 折交叉验证...")
    print("=" * 50)
    
    cv_results = {}
    
    try:
        from sklearn.model_selection import KFold
        
        # 加载数据
        X, y = predictor.load_training_data()
        
        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        
        for model_name in models_to_evaluate:
            if model_name in predictor.models:
                print(f"\n交叉验证模型: {model_name}")
                print("-" * 30)
                
                fold_results = []
                
                for fold, (train_idx, val_idx) in enumerate(kf.split(X), 1):
                    print(f"  折 {fold}/{cv_folds}")
                    
                    X_train, X_val = X[train_idx], X[val_idx]
                    y_train, y_val = y[train_idx], y[val_idx]
                    
                    try:
                        # 重新训练模型
                        model = predictor.models[model_name]
                        model.train(X_train, y_train)
                        
                        # 评估
                        predictions = model.predict(X_val)
                        mae = float(np.mean(np.abs(y_val - predictions)))
                        accuracy = float(np.mean(np.abs(y_val - predictions) <= 1))
                        
                        fold_results.append({
                            'fold': fold,
                            'mae': mae,
                            'accuracy': accuracy,
                            'samples': len(y_val)
                        })
                        
                        print(f"    MAE: {mae:.4f}, 准确率: {accuracy:.4f}")
                        
                    except Exception as e:
                        print(f"    错误: {str(e)}")
                        fold_results.append({
                            'fold': fold,
                            'error': str(e)
                        })
                
                # 计算平均结果
                valid_results = [r for r in fold_results if 'error' not in r]
                if valid_results:
                    avg_mae = np.mean([r['mae'] for r in valid_results])
                    avg_accuracy = np.mean([r['accuracy'] for r in valid_results])
                    std_mae = np.std([r['mae'] for r in valid_results])
                    std_accuracy = np.std([r['accuracy'] for r in valid_results])
                    
                    cv_results[model_name] = {
                        'fold_results': fold_results,
                        'avg_mae': float(avg_mae),
                        'avg_accuracy': float(avg_accuracy),
                        'std_mae': float(std_mae),
                        'std_accuracy': float(std_accuracy),
                        'valid_folds': len(valid_results)
                    }
                    
                    print(f"  平均 MAE: {avg_mae:.4f} ± {std_mae:.4f}")
                    print(f"  平均准确率: {avg_accuracy:.4f} ± {std_accuracy:.4f}")
                else:
                    cv_results[model_name] = {'error': 'all_folds_failed'}
        
        return cv_results
        
    except Exception as e:
        print(f"交叉验证失败: {str(e)}")
        return {'error': str(e)}

def generate_evaluation_report(performance_results: Dict[str, Any], 
                             constraint_results: Optional[Dict[str, Any]] = None,
                             pattern_results: Optional[Dict[str, Any]] = None,
                             cv_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """生成评估报告"""
    report = {
        'evaluation_timestamp': time.time(),
        'model_performance': performance_results
    }
    
    if constraint_results:
        report['constraint_evaluation'] = constraint_results
    
    if pattern_results:
        report['pattern_analysis'] = pattern_results
    
    if cv_results:
        report['cross_validation'] = cv_results
    
    # 生成摘要
    summary = {
        'total_models_evaluated': len(performance_results),
        'successful_evaluations': len([r for r in performance_results.values() if 'error' not in r]),
        'failed_evaluations': len([r for r in performance_results.values() if 'error' in r])
    }
    
    # 找出最佳模型
    valid_results = {k: v for k, v in performance_results.items() if 'error' not in v and 'status' not in v}
    if valid_results:
        best_mae_model = min(valid_results.items(), key=lambda x: x[1].get('mae', float('inf')))
        best_accuracy_model = max(valid_results.items(), key=lambda x: x[1].get('accuracy_1', 0))
        
        summary['best_mae_model'] = {
            'model': best_mae_model[0],
            'mae': best_mae_model[1].get('mae', 0)
        }
        summary['best_accuracy_model'] = {
            'model': best_accuracy_model[0],
            'accuracy': best_accuracy_model[1].get('accuracy_1', 0)
        }
    
    report['summary'] = summary
    
    return report

def print_evaluation_summary(report: Dict[str, Any]):
    """打印评估摘要"""
    print("\n" + "=" * 80)
    print("评估摘要")
    print("=" * 80)
    
    summary = report.get('summary', {})
    
    print(f"评估模型数量: {summary.get('total_models_evaluated', 0)}")
    print(f"成功评估: {summary.get('successful_evaluations', 0)}")
    print(f"评估失败: {summary.get('failed_evaluations', 0)}")
    
    if 'best_mae_model' in summary:
        best_mae = summary['best_mae_model']
        print(f"最佳MAE模型: {best_mae['model']} (MAE: {best_mae['mae']:.4f})")
    
    if 'best_accuracy_model' in summary:
        best_acc = summary['best_accuracy_model']
        print(f"最佳准确率模型: {best_acc['model']} (准确率: {best_acc['accuracy']:.4f})")
    
    print("=" * 80)

def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志
        setup_logging(args.log_level)
        
        # 验证参数
        validate_arguments(args)
        
        # 初始化预测器
        print("初始化跨度预测器...")
        predictor = SpanPredictor(args.db_path, args.config_path)
        
        # 构建模型
        if not predictor.build_model():
            raise RuntimeError("模型构建失败")
        
        # 加载模型
        load_results = load_predictor_models(predictor, args.model_dir, args.models)
        
        # 确定要评估的模型
        models_to_evaluate = [model for model, success in load_results.items() if success]
        
        if not models_to_evaluate:
            raise RuntimeError("没有可用的训练模型")
        
        print(f"将评估 {len(models_to_evaluate)} 个模型: {', '.join(models_to_evaluate)}")
        
        # 执行评估
        performance_results = evaluate_model_performance(predictor, models_to_evaluate, args.test_size)
        
        constraint_results = None
        if args.evaluate_constraints:
            constraint_results = evaluate_constraint_effectiveness(predictor, args.test_size)
        
        pattern_results = None
        if args.evaluate_patterns:
            pattern_results = evaluate_pattern_analysis(predictor)
        
        cv_results = None
        if args.cross_validation:
            cv_results = perform_cross_validation(predictor, models_to_evaluate, args.cv_folds)
        
        # 生成报告
        report = generate_evaluation_report(
            performance_results, constraint_results, pattern_results, cv_results
        )
        
        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"\n评估报告已保存到: {args.output}")
        
        # 打印摘要
        print_evaluation_summary(report)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n⚠️  评估被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 评估失败: {str(e)}")
        logging.error(f"评估脚本执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

# P6-P7预测器个人开发环境测试指南

## 🎯 测试环境要求

**Python版本**: 3.11.9 (已确认)  
**环境类型**: 个人开发环境 (非Anaconda/Docker)  
**操作系统**: Windows  
**项目路径**: D:\github\fucai3d  

## 📋 测试前准备

### 1. 确认Python环境
```bash
# 检查Python版本
python --version
# 应该显示: Python 3.11.9

# 检查pip
pip --version
```

### 2. 安装必需依赖
```bash
# 基础依赖 (必需)
pip install pandas numpy pyyaml

# 机器学习依赖 (可选，用于完整功能)
pip install scikit-learn

# XGBoost和LightGBM (可选，用于高级模型)
pip install xgboost lightgbm

# TensorFlow (可选，用于LSTM模型)
pip install tensorflow
```

### 3. 检查项目文件
确认以下关键文件存在：
- `data/lottery.db` (数据库文件)
- `src/predictors/sum_predictor.py` (P6主预测器)
- `src/predictors/span_predictor.py` (P7主预测器)
- `config/sum_predictor_config.yaml` (P6配置)
- `config/span_predictor_config.yaml` (P7配置)

## 🧪 分步测试方案

### 测试1: 基础环境验证
```bash
# 测试Python基础功能
python -c "print('Python环境正常')"

# 测试基础库导入
python -c "import sys, os, sqlite3, json; print('基础库导入成功')"

# 测试pandas导入
python -c "import pandas as pd; print(f'pandas版本: {pd.__version__}')"

# 测试numpy导入
python -c "import numpy as np; print(f'numpy版本: {np.__version__}')"
```

### 测试2: 数据库连接测试
```bash
# 创建数据库测试脚本
python -c "
import sqlite3
from pathlib import Path

db_path = 'data/lottery.db'
if Path(db_path).exists():
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM lottery_records')
    count = cursor.fetchone()[0]
    print(f'数据库连接成功，记录数: {count}')
    
    # 显示最新3条数据
    cursor.execute('SELECT period, numbers FROM lottery_records ORDER BY id DESC LIMIT 3')
    for row in cursor.fetchall():
        period, numbers = row
        print(f'期号: {period}, 号码: {numbers}')
    conn.close()
else:
    print('数据库文件不存在')
"
```

### 测试3: 项目代码导入测试
```bash
# 测试数据访问层导入
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

try:
    from src.data.sum_data_access import SumDataAccess
    print('✅ SumDataAccess导入成功')
except Exception as e:
    print(f'❌ SumDataAccess导入失败: {e}')

try:
    from src.data.span_data_access import SpanDataAccess
    print('✅ SpanDataAccess导入成功')
except Exception as e:
    print(f'❌ SpanDataAccess导入失败: {e}')
"

# 测试预测器导入
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

try:
    from src.predictors.sum_predictor import SumPredictor
    print('✅ SumPredictor导入成功')
except Exception as e:
    print(f'❌ SumPredictor导入失败: {e}')

try:
    from src.predictors.span_predictor import SpanPredictor
    print('✅ SpanPredictor导入成功')
except Exception as e:
    print(f'❌ SpanPredictor导入失败: {e}')
"
```

### 测试4: 数据加载功能测试
```bash
# 测试P6数据加载
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

from src.data.sum_data_access import SumDataAccess

try:
    data_access = SumDataAccess('data/lottery.db')
    df = data_access.load_lottery_data(limit=5)
    print(f'✅ P6数据加载成功: {len(df)} 条记录')
    print('数据样本:')
    print(df.head())
except Exception as e:
    print(f'❌ P6数据加载失败: {e}')
    import traceback
    traceback.print_exc()
"

# 测试P7数据加载
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

from src.data.span_data_access import SpanDataAccess

try:
    data_access = SpanDataAccess('data/lottery.db')
    df = data_access.load_lottery_data(limit=5)
    print(f'✅ P7数据加载成功: {len(df)} 条记录')
    print('数据样本:')
    print(df.head())
except Exception as e:
    print(f'❌ P7数据加载失败: {e}')
    import traceback
    traceback.print_exc()
"
```

### 测试5: 预测器初始化测试
```bash
# 测试P6预测器初始化
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

from src.predictors.sum_predictor import SumPredictor

try:
    predictor = SumPredictor('data/lottery.db')
    print(f'✅ P6预测器初始化成功')
    print(f'位置: {predictor.position}')
    print(f'预测范围: {predictor.prediction_range}')
    print(f'目标类型: {predictor.target_type}')
except Exception as e:
    print(f'❌ P6预测器初始化失败: {e}')
    import traceback
    traceback.print_exc()
"

# 测试P7预测器初始化
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

from src.predictors.span_predictor import SpanPredictor

try:
    predictor = SpanPredictor('data/lottery.db')
    print(f'✅ P7预测器初始化成功')
    print(f'位置: {predictor.position}')
    print(f'预测范围: {predictor.prediction_range}')
    print(f'目标类型: {predictor.target_type}')
    
    # 测试专属功能
    predictor.enable_dual_constraints(True)
    predictor.enable_pattern_analysis(True)
    config = predictor.get_constraint_configuration()
    print(f'约束配置: {config}')
    
except Exception as e:
    print(f'❌ P7预测器初始化失败: {e}')
    import traceback
    traceback.print_exc()
"
```

### 测试6: 模型构建测试 (可选)
```bash
# 测试P6模型构建 (需要机器学习库)
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

from src.predictors.sum_predictor import SumPredictor

try:
    predictor = SumPredictor('data/lottery.db')
    success = predictor.build_model()
    if success:
        models = predictor.get_available_models()
        print(f'✅ P6模型构建成功，可用模型: {models}')
    else:
        print('⚠️ P6模型构建失败')
except Exception as e:
    print(f'❌ P6模型构建异常: {e}')
"

# 测试P7模型构建 (需要机器学习库)
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

from src.predictors.span_predictor import SpanPredictor

try:
    predictor = SpanPredictor('data/lottery.db')
    success = predictor.build_model()
    if success:
        models = predictor.get_available_models()
        print(f'✅ P7模型构建成功，可用模型: {models}')
    else:
        print('⚠️ P7模型构建失败')
except Exception as e:
    print(f'❌ P7模型构建异常: {e}')
"
```

## 🎯 预期测试结果

### 成功标准
- ✅ 所有基础库导入成功
- ✅ 数据库连接正常，能读取历史数据
- ✅ 项目代码导入无错误
- ✅ 数据加载功能正常，返回正确格式的DataFrame
- ✅ 预测器初始化成功，属性设置正确
- ✅ (可选) 模型构建成功，返回可用模型列表

### 常见问题解决

#### 问题1: pandas导入失败
```bash
# 解决方案
pip install --upgrade pandas
# 或者
pip uninstall pandas
pip install pandas
```

#### 问题2: 项目代码导入失败
```bash
# 确认在项目根目录
cd D:\github\fucai3d
# 检查文件是否存在
dir src\predictors\sum_predictor.py
```

#### 问题3: 数据库连接失败
```bash
# 检查数据库文件
dir data\lottery.db
# 检查文件大小 (应该不为0)
```

#### 问题4: 机器学习库缺失
```bash
# 安装可选依赖
pip install scikit-learn xgboost lightgbm
# 注意: TensorFlow较大，可以最后安装
pip install tensorflow
```

## 📊 测试报告模板

测试完成后，请记录以下信息：

```
P6-P7预测器个人环境测试报告
=====================================
测试时间: [填写时间]
Python版本: [python --version结果]
项目路径: D:\github\fucai3d

测试结果:
□ 测试1: 基础环境验证 - [通过/失败]
□ 测试2: 数据库连接测试 - [通过/失败]  
□ 测试3: 项目代码导入测试 - [通过/失败]
□ 测试4: 数据加载功能测试 - [通过/失败]
□ 测试5: 预测器初始化测试 - [通过/失败]
□ 测试6: 模型构建测试 - [通过/失败/跳过]

问题记录:
[记录遇到的问题和解决方案]

总体评估:
[成功/部分成功/失败]
```

## 🚀 下一步建议

### 测试通过后
1. 尝试运行训练脚本: `python scripts/train_sum_predictor.py --help`
2. 尝试运行预测脚本: `python scripts/predict_span.py --help`
3. 查看详细文档: `docs/P7_SPAN_PREDICTOR_README.md`

### 测试失败时
1. 记录具体错误信息
2. 检查依赖包安装
3. 确认项目文件完整性
4. 参考常见问题解决方案

---

**重要提示**: 这些测试命令都是为个人Python 3.11.9开发环境设计的，避免了Anaconda和Docker的复杂性，专注于验证核心功能。

"""
P7跨度预测器 - 基础模型类

定义跨度预测模型的基础接口和通用功能
所有跨度预测模型都应继承此类

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import yaml

from ...data.span_data_access import SpanDataAccess

class BaseSpanModel(ABC):
    """跨度预测模型基类"""
    
    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化基础跨度模型
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        self.db_path = db_path
        self.config_path = config_path
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化数据访问层
        self.data_access = SpanDataAccess(db_path)
        
        # 加载配置
        self.config = self._load_config()
        
        # 模型基础属性
        self.model_type = None
        self.model = None
        self.is_trained = False
        self.position = 'span'
        self.prediction_range = (0, 9)  # 跨度范围0-9
        self.target_type = 'regression'
        
        # 跨度专属属性
        self.span_range = list(range(10))  # 0-9
        self.pattern_types = ['ascending', 'descending', 'same_digit', 'consecutive']
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self.config_path and Path(self.config_path).exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            except Exception as e:
                self.logger.warning(f"加载配置文件失败: {e}")
        
        return {}
    
    @abstractmethod
    def build_model(self) -> bool:
        """
        构建模型
        
        Returns:
            是否构建成功
        """
        pass
    
    @abstractmethod
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X: 特征矩阵（可选）
            y: 目标向量（可选）
            
        Returns:
            训练性能指标
        """
        pass
    
    @abstractmethod
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测跨度
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果
        """
        pass
    
    @abstractmethod
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            是否保存成功
        """
        pass
    
    @abstractmethod
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        pass
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        带置信度的预测（默认实现）
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果和置信度
        """
        predictions = self.predict(X)
        confidences = np.full(len(predictions), 0.7)  # 默认置信度
        return predictions, confidences
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            X_test: 测试特征
            y_test: 测试目标
            
        Returns:
            性能指标字典
        """
        try:
            predictions = self.predict(X_test)
            
            # 基础回归指标
            mae = float(np.mean(np.abs(y_test - predictions)))
            rmse = float(np.sqrt(np.mean((y_test - predictions) ** 2)))
            
            # 准确率指标
            accuracy = float(np.mean(np.abs(y_test - predictions) <= 0.5))  # ±0.5准确率
            accuracy_1 = float(np.mean(np.abs(y_test - predictions) <= 1))  # ±1准确率
            accuracy_2 = float(np.mean(np.abs(y_test - predictions) <= 2))  # ±2准确率
            
            # R²分数
            ss_res = np.sum((y_test - predictions) ** 2)
            ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
            r2_score = float(1 - (ss_res / ss_tot)) if ss_tot != 0 else 0.0
            
            # 置信度指标
            _, confidences = self.predict_with_confidence(X_test)
            avg_confidence = float(np.mean(confidences))
            
            return {
                'accuracy': accuracy,
                'mae': mae,
                'rmse': rmse,
                'accuracy_1': accuracy_1,
                'accuracy_2': accuracy_2,
                'r2_score': r2_score,
                'avg_confidence': avg_confidence
            }
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            return {
                'accuracy': 0.0,
                'mae': 10.0,
                'rmse': 10.0,
                'accuracy_1': 0.0,
                'accuracy_2': 0.0,
                'r2_score': 0.0,
                'avg_confidence': 0.0
            }
    
    def analyze_span_patterns(self, hundreds: int, tens: int, units: int) -> Dict[str, Any]:
        """
        分析跨度模式
        
        Args:
            hundreds: 百位数字
            tens: 十位数字
            units: 个位数字
            
        Returns:
            模式分析结果
        """
        return self.data_access.analyze_span_patterns(hundreds, tens, units)
    
    def calculate_span(self, hundreds: int, tens: int, units: int) -> int:
        """
        计算跨度
        
        Args:
            hundreds: 百位数字
            tens: 十位数字
            units: 个位数字
            
        Returns:
            跨度值
        """
        digits = [hundreds, tens, units]
        return max(digits) - min(digits)
    
    def validate_prediction(self, prediction: float) -> float:
        """
        验证和修正预测结果
        
        Args:
            prediction: 原始预测值
            
        Returns:
            修正后的预测值
        """
        # 跨度范围约束
        return float(np.clip(prediction, 0, 9))
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'model_type': self.model_type,
            'position': self.position,
            'prediction_range': self.prediction_range,
            'target_type': self.target_type,
            'is_trained': self.is_trained,
            'span_range': self.span_range,
            'pattern_types': self.pattern_types
        }
    
    def prepare_prediction_result(self, issue: str, prediction: float, 
                                confidence: float, **kwargs) -> Dict[str, Any]:
        """
        准备预测结果字典
        
        Args:
            issue: 期号
            prediction: 预测值
            confidence: 置信度
            **kwargs: 其他参数
            
        Returns:
            预测结果字典
        """
        result = {
            'issue': issue,
            'model_type': self.model_type,
            'predicted_digit': self.validate_prediction(prediction),
            'confidence': confidence,
            'prediction_range_min': max(0, int(prediction - 1)),
            'prediction_range_max': min(9, int(prediction + 1))
        }
        
        # 添加其他参数
        result.update(kwargs)
        
        return result
    
    def save_prediction_result(self, prediction_result: Dict[str, Any]) -> bool:
        """
        保存预测结果到数据库
        
        Args:
            prediction_result: 预测结果字典
            
        Returns:
            是否保存成功
        """
        return self.data_access.save_prediction_result(prediction_result)
    
    def get_prediction_history(self, limit: int = 100) -> List[Dict]:
        """
        获取预测历史
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            预测历史记录列表
        """
        return self.data_access.get_prediction_history(self.model_type, limit)
    
    def save_performance_metrics(self, performance_data: Dict[str, Any]) -> bool:
        """
        保存性能指标到数据库
        
        Args:
            performance_data: 性能数据字典
            
        Returns:
            是否保存成功
        """
        # 添加模型类型
        performance_data['model_type'] = self.model_type
        return self.data_access.save_performance_metrics(performance_data)
    
    def get_performance_history(self, limit: int = 50) -> List[Dict]:
        """
        获取性能历史
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            性能历史记录列表
        """
        return self.data_access.get_performance_history(self.model_type, limit)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(model_type={self.model_type}, is_trained={self.is_trained})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()

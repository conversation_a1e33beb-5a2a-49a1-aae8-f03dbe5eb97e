#!/usr/bin/env python3
"""
P7跨度预测器测试文件

测试所有模型和功能，确保代码质量
包括单元测试、集成测试和性能测试

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import unittest
import tempfile
import sqlite3
import numpy as np
import pandas as pd
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.span_predictor import SpanPredictor
from src.data.span_data_access import SpanDataAccess

class TestSpanDataAccess(unittest.TestCase):
    """测试跨度数据访问层"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建测试数据库
        self.create_test_database()
        
        # 初始化数据访问层
        self.data_access = SpanDataAccess(self.db_path)
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def create_test_database(self):
        """创建测试数据库和数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建基础表
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入测试数据
        test_data = [
            ('2024001', 1, 2, 3),
            ('2024002', 4, 5, 6),
            ('2024003', 7, 8, 9),
            ('2024004', 0, 1, 2),
            ('2024005', 3, 3, 3),
            ('2024006', 1, 3, 5),
            ('2024007', 2, 4, 6),
            ('2024008', 0, 5, 9),
            ('2024009', 1, 1, 1),
            ('2024010', 2, 5, 8)
        ]
        
        cursor.executemany(
            'INSERT INTO lottery_data (issue, hundreds, tens, units) VALUES (?, ?, ?, ?)',
            test_data
        )
        
        conn.commit()
        conn.close()
    
    def test_load_lottery_data(self):
        """测试加载彩票数据"""
        data = self.data_access.load_lottery_data()
        
        self.assertFalse(data.empty)
        self.assertEqual(len(data), 10)
        self.assertIn('hundreds', data.columns)
        self.assertIn('tens', data.columns)
        self.assertIn('units', data.columns)
    
    def test_analyze_span_patterns(self):
        """测试跨度模式分析"""
        # 测试升序模式
        result = self.data_access.analyze_span_patterns(1, 2, 3)
        self.assertEqual(result['span'], 2)
        self.assertTrue(result['patterns']['ascending'])
        self.assertFalse(result['patterns']['same_digit'])
        
        # 测试相同数字模式
        result = self.data_access.analyze_span_patterns(3, 3, 3)
        self.assertEqual(result['span'], 0)
        self.assertTrue(result['patterns']['same_digit'])
        self.assertFalse(result['patterns']['ascending'])
    
    def test_save_prediction_result(self):
        """测试保存预测结果"""
        prediction_result = {
            'issue': '2024011',
            'model_type': 'test',
            'predicted_digit': 5.5,
            'confidence': 0.8,
            'probabilities': [0.1] * 10,
            'prediction_range_min': 4,
            'prediction_range_max': 7,
            'pattern_analysis': {'test': 'data'}
        }
        
        success = self.data_access.save_prediction_result(prediction_result)
        self.assertTrue(success)
    
    def test_get_data_statistics(self):
        """测试获取数据统计"""
        stats = self.data_access.get_data_statistics()
        
        self.assertIn('total_predictions', stats)
        self.assertIn('model_count', stats)
        self.assertIn('issue_count', stats)

class TestSpanPredictor(unittest.TestCase):
    """测试跨度预测器主类"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建测试数据库
        self.create_test_database()
        
        # 初始化预测器
        self.predictor = SpanPredictor(self.db_path)
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def create_test_database(self):
        """创建测试数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建基础表
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入更多测试数据用于训练
        test_data = []
        for i in range(100):
            issue = f'2024{i+1:03d}'
            hundreds = np.random.randint(0, 10)
            tens = np.random.randint(0, 10)
            units = np.random.randint(0, 10)
            test_data.append((issue, hundreds, tens, units))
        
        cursor.executemany(
            'INSERT INTO lottery_data (issue, hundreds, tens, units) VALUES (?, ?, ?, ?)',
            test_data
        )
        
        conn.commit()
        conn.close()
    
    def test_initialization(self):
        """测试预测器初始化"""
        self.assertEqual(self.predictor.position, 'span')
        self.assertEqual(self.predictor.prediction_range, (0, 9))
        self.assertEqual(self.predictor.target_type, 'regression')
        self.assertIsNotNone(self.predictor.data_access)
    
    def test_build_model(self):
        """测试模型构建"""
        success = self.predictor.build_model()
        self.assertTrue(success)
        
        # 检查所有模型是否创建
        expected_models = ['xgb', 'lgb', 'lstm', 'classification', 'constraint', 'ensemble']
        for model_name in expected_models:
            self.assertIn(model_name, self.predictor.models)
    
    def test_get_available_models(self):
        """测试获取可用模型列表"""
        self.predictor.build_model()
        models = self.predictor.get_available_models()
        
        self.assertIsInstance(models, list)
        self.assertGreater(len(models), 0)
        self.assertIn('ensemble', models)
    
    def test_switch_model(self):
        """测试模型切换"""
        self.predictor.build_model()
        
        # 切换到XGBoost模型
        self.predictor.switch_model('xgb')
        self.assertEqual(self.predictor.current_model, 'xgb')
        
        # 测试切换到不存在的模型
        with self.assertRaises(ValueError):
            self.predictor.switch_model('nonexistent')
    
    def test_get_model_info(self):
        """测试获取模型信息"""
        self.predictor.build_model()
        info = self.predictor.get_model_info()
        
        self.assertIn('position', info)
        self.assertIn('current_model', info)
        self.assertIn('available_models', info)
        self.assertIn('dual_constraints_enabled', info)
        self.assertIn('pattern_analysis_enabled', info)
        
        self.assertEqual(info['position'], 'span')
    
    def test_constraint_configuration(self):
        """测试约束配置"""
        # 测试启用双重约束
        self.predictor.enable_dual_constraints(True)
        config = self.predictor.get_constraint_configuration()
        self.assertTrue(config['dual_constraints_enabled'])
        
        # 测试禁用双重约束
        self.predictor.enable_dual_constraints(False)
        config = self.predictor.get_constraint_configuration()
        self.assertFalse(config['dual_constraints_enabled'])
    
    def test_pattern_analysis_configuration(self):
        """测试模式分析配置"""
        # 测试启用模式分析
        self.predictor.enable_pattern_analysis(True)
        self.assertTrue(self.predictor.pattern_analysis_enabled)
        
        # 测试禁用模式分析
        self.predictor.enable_pattern_analysis(False)
        self.assertFalse(self.predictor.pattern_analysis_enabled)
    
    @patch('src.predictors.span_predictor.SpanPredictor._get_prediction_features')
    def test_predict_pattern_probability(self, mock_features):
        """测试模式概率预测"""
        # 测试升序模式
        result = self.predictor.predict_pattern_probability([1, 2, 3])
        self.assertEqual(result['predicted_span'], 2)
        self.assertEqual(result['ascending'], 1.0)
        self.assertEqual(result['same_digit'], 0.0)
        
        # 测试相同数字模式
        result = self.predictor.predict_pattern_probability([5, 5, 5])
        self.assertEqual(result['predicted_span'], 0)
        self.assertEqual(result['same_digit'], 1.0)
        self.assertEqual(result['ascending'], 0.0)
    
    def test_calculate_constraint_consistency_score(self):
        """测试约束一致性评分计算"""
        position_predictions = {'hundreds': 1, 'tens': 2, 'units': 3}
        sum_prediction = 6.0
        span_prediction = 2.0
        
        scores = self.predictor.calculate_constraint_consistency_score(
            span_prediction, position_predictions, sum_prediction
        )
        
        self.assertIn('position_consistency', scores)
        self.assertIn('sum_consistency', scores)
        self.assertIn('dual_consistency', scores)
        
        # 检查分数范围
        for score in scores.values():
            if isinstance(score, (int, float)):
                self.assertGreaterEqual(score, 0.0)
                self.assertLessEqual(score, 1.0)

class TestSpanModels(unittest.TestCase):
    """测试跨度预测模型"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建测试数据
        self.create_test_data()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def create_test_data(self):
        """创建测试数据"""
        # 生成模拟训练数据
        np.random.seed(42)
        self.X = np.random.rand(100, 10)  # 100个样本，10个特征
        self.y = np.random.randint(0, 10, 100)  # 跨度值0-9
    
    def test_xgb_model_interface(self):
        """测试XGBoost模型接口"""
        from src.predictors.models.xgb_span_model import XGBSpanModel
        
        try:
            model = XGBSpanModel(self.db_path)
            
            # 测试模型构建
            success = model.build_model()
            self.assertTrue(success)
            
            # 测试模型信息
            info = model.get_model_info()
            self.assertEqual(info['model_type'], 'xgb')
            
        except ImportError:
            self.skipTest("XGBoost未安装")
    
    def test_lgb_model_interface(self):
        """测试LightGBM模型接口"""
        from src.predictors.models.lgb_span_model import LGBSpanModel
        
        try:
            model = LGBSpanModel(self.db_path)
            
            # 测试模型构建
            success = model.build_model()
            self.assertTrue(success)
            
            # 测试模型信息
            info = model.get_model_info()
            self.assertEqual(info['model_type'], 'lgb')
            
        except ImportError:
            self.skipTest("LightGBM未安装")
    
    def test_lstm_model_interface(self):
        """测试LSTM模型接口"""
        from src.predictors.models.lstm_span_model import LSTMSpanModel
        
        try:
            model = LSTMSpanModel(self.db_path)
            
            # 测试模型构建
            success = model.build_model()
            self.assertTrue(success)
            
            # 测试模型信息
            info = model.get_model_info()
            self.assertEqual(info['model_type'], 'lstm')
            
        except ImportError:
            self.skipTest("TensorFlow未安装")
    
    def test_classification_model_interface(self):
        """测试分类模型接口"""
        from src.predictors.models.classification_span_model import ClassificationSpanModel
        
        try:
            model = ClassificationSpanModel(self.db_path)
            
            # 测试模型构建
            success = model.build_model()
            self.assertTrue(success)
            
            # 测试模型信息
            info = model.get_model_info()
            self.assertEqual(info['model_type'], 'classification')
            
            # 测试分类专属属性
            self.assertEqual(model.num_classes, 10)
            self.assertEqual(len(model.class_labels), 10)
            
        except ImportError:
            self.skipTest("XGBoost未安装")
    
    def test_constraint_model_interface(self):
        """测试约束模型接口"""
        from src.predictors.models.constraint_span_model import ConstraintSpanModel
        
        model = ConstraintSpanModel(self.db_path)
        
        # 测试模型构建
        success = model.build_model()
        self.assertTrue(success)
        
        # 测试模型信息
        info = model.get_model_info()
        self.assertEqual(info['model_type'], 'constraint')
    
    def test_ensemble_model_interface(self):
        """测试集成模型接口"""
        from src.predictors.models.ensemble_span_model import EnsembleSpanModel
        
        model = EnsembleSpanModel(self.db_path)
        
        # 测试模型构建
        success = model.build_model()
        self.assertTrue(success)
        
        # 测试模型信息
        info = model.get_model_info()
        self.assertEqual(info['model_type'], 'ensemble')
        
        # 测试权重管理
        weights = model.get_model_weights()
        self.assertIsInstance(weights, dict)

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建完整的测试数据库
        self.create_full_test_database()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def create_full_test_database(self):
        """创建完整的测试数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建所有必要的表
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入足够的测试数据
        test_data = []
        np.random.seed(42)
        for i in range(200):
            issue = f'2024{i+1:03d}'
            hundreds = np.random.randint(0, 10)
            tens = np.random.randint(0, 10)
            units = np.random.randint(0, 10)
            test_data.append((issue, hundreds, tens, units))
        
        cursor.executemany(
            'INSERT INTO lottery_data (issue, hundreds, tens, units) VALUES (?, ?, ?, ?)',
            test_data
        )
        
        conn.commit()
        conn.close()
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 初始化预测器
        predictor = SpanPredictor(self.db_path)
        
        # 构建模型
        success = predictor.build_model()
        self.assertTrue(success)
        
        # 加载数据
        X, y = predictor.load_training_data()
        self.assertGreater(len(X), 0)
        self.assertGreater(len(y), 0)
        
        # 测试预测概率分布
        probabilities = predictor.predict_probability(X[:5])
        self.assertEqual(probabilities.shape, (5, 10))
        
        # 测试约束配置
        predictor.enable_dual_constraints(True)
        predictor.enable_pattern_analysis(True)
        
        config = predictor.get_constraint_configuration()
        self.assertTrue(config['dual_constraints_enabled'])
        self.assertTrue(config['pattern_analysis_enabled'])

def run_performance_tests():
    """运行性能测试"""
    print("\n" + "="*50)
    print("性能测试")
    print("="*50)
    
    # 创建临时数据库
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    db_path = temp_db.name
    temp_db.close()
    
    try:
        # 创建大量测试数据
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入1000条测试数据
        test_data = []
        np.random.seed(42)
        for i in range(1000):
            issue = f'2024{i+1:04d}'
            hundreds = np.random.randint(0, 10)
            tens = np.random.randint(0, 10)
            units = np.random.randint(0, 10)
            test_data.append((issue, hundreds, tens, units))
        
        cursor.executemany(
            'INSERT INTO lottery_data (issue, hundreds, tens, units) VALUES (?, ?, ?, ?)',
            test_data
        )
        
        conn.commit()
        conn.close()
        
        # 性能测试
        import time
        
        predictor = SpanPredictor(db_path)
        
        # 测试数据加载性能
        start_time = time.time()
        X, y = predictor.load_training_data()
        load_time = time.time() - start_time
        print(f"数据加载时间: {load_time:.3f}秒 ({len(X)} 个样本)")
        
        # 测试模型构建性能
        start_time = time.time()
        predictor.build_model()
        build_time = time.time() - start_time
        print(f"模型构建时间: {build_time:.3f}秒")
        
        # 测试预测性能
        start_time = time.time()
        probabilities = predictor.predict_probability(X[:100])
        predict_time = time.time() - start_time
        print(f"预测时间: {predict_time:.3f}秒 (100个样本)")
        print(f"平均预测时间: {predict_time/100*1000:.2f}毫秒/样本")
        
        # 测试模式分析性能
        start_time = time.time()
        pattern_result = predictor.predict_pattern_probability([1, 2, 3])
        pattern_time = time.time() - start_time
        print(f"模式分析时间: {pattern_time*1000:.2f}毫秒")
        
        print("性能测试完成")
        
    finally:
        if os.path.exists(db_path):
            os.unlink(db_path)

def main():
    """主函数"""
    print("P7跨度预测器测试套件")
    print("="*50)
    
    # 运行单元测试
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestSpanDataAccess))
    suite.addTests(loader.loadTestsFromTestCase(TestSpanPredictor))
    suite.addTests(loader.loadTestsFromTestCase(TestSpanModels))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 运行性能测试
    run_performance_tests()
    
    # 输出测试结果摘要
    print("\n" + "="*50)
    print("测试结果摘要")
    print("="*50)
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n测试结果: {'通过' if success else '失败'}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

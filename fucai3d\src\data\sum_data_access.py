#!/usr/bin/env python3
"""
和值预测器数据访问层

提供和值预测器相关的数据库操作功能，包括：
- 预测结果的保存和查询
- 模型性能数据的管理
- 和值分布统计和约束规则管理

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sqlite3
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

class SumDataAccess:
    """和值预测器数据访问类"""

    def __init__(self, db_path: str):
        """
        初始化数据访问层

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger("SumDataAccess")

        # 验证数据库文件存在
        if not Path(db_path).exists():
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")

        # 验证必要的表是否存在
        self._verify_tables()

    def save_prediction_result(self, prediction_result: Dict[str, Any]) -> bool:
        """
        保存预测结果

        Args:
            prediction_result: 预测结果字典

        Returns:
            保存是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT OR REPLACE INTO sum_predictions
                (issue, model_type, predicted_digit, confidence,
                 prediction_range_min, prediction_range_max,
                 distribution_entropy, constraint_score, probabilities)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                prediction_result['issue'],
                prediction_result['model_type'],
                prediction_result['predicted_digit'],
                prediction_result['confidence'],
                prediction_result.get('prediction_range_min'),
                prediction_result.get('prediction_range_max'),
                prediction_result.get('distribution_entropy'),
                prediction_result.get('constraint_score'),
                json.dumps(prediction_result.get('probabilities', []))
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"保存预测结果成功: {prediction_result['issue']}")
            return True

        except Exception as e:
            self.logger.error(f"保存预测结果失败: {e}")
            return False

    def get_prediction_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取预测历史

        Args:
            limit: 返回记录数量限制

        Returns:
            预测历史列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT issue, model_type, predicted_digit, confidence,
                       prediction_range_min, prediction_range_max,
                       distribution_entropy, constraint_score, probabilities, created_at
                FROM sum_predictions
                ORDER BY created_at DESC
                LIMIT ?
            """, (limit,))

            rows = cursor.fetchall()
            conn.close()

            results = []
            for row in rows:
                results.append({
                    'issue': row[0],
                    'model_type': row[1],
                    'predicted_digit': row[2],
                    'confidence': row[3],
                    'prediction_range_min': row[4],
                    'prediction_range_max': row[5],
                    'distribution_entropy': row[6],
                    'constraint_score': row[7],
                    'probabilities': json.loads(row[8]) if row[8] else [],
                    'created_at': row[9]
                })

            return results

        except Exception as e:
            self.logger.error(f"获取预测历史失败: {e}")
            return []

    def save_performance_metrics(self, performance_data: Dict[str, Any]) -> bool:
        """
        保存模型性能指标

        Args:
            performance_data: 性能数据字典

        Returns:
            保存是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO sum_model_performance
                (model_type, evaluation_period, accuracy, mae, rmse, accuracy_1,
                 accuracy_2, r2_score, distribution_accuracy, avg_confidence,
                 training_time, prediction_time, model_size)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                performance_data['model_type'],
                performance_data['evaluation_period'],
                performance_data['accuracy'],
                performance_data['mae'],
                performance_data['rmse'],
                performance_data['accuracy_1'],
                performance_data['accuracy_2'],
                performance_data['r2_score'],
                performance_data.get('distribution_accuracy', 0.0),
                performance_data.get('avg_confidence', 0.0),
                performance_data.get('training_time', 0.0),
                performance_data.get('prediction_time', 0.0),
                performance_data.get('model_size', 0)
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"保存性能指标成功: {performance_data['model_type']}")
            return True

        except Exception as e:
            self.logger.error(f"保存性能指标失败: {e}")
            return False

    def get_performance_history(self, model_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取性能历史"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if model_type:
                cursor.execute("""
                    SELECT * FROM sum_model_performance 
                    WHERE model_type = ? 
                    ORDER BY evaluated_at DESC
                """, (model_type,))
            else:
                cursor.execute("""
                    SELECT * FROM sum_model_performance 
                    ORDER BY evaluated_at DESC
                """)

            rows = cursor.fetchall()
            conn.close()

            # 转换为字典列表
            columns = ['id', 'model_type', 'evaluation_period', 'accuracy', 'mae', 'rmse',
                      'accuracy_1', 'accuracy_2', 'r2_score', 'distribution_accuracy',
                      'avg_confidence', 'training_time', 'prediction_time', 'model_size',
                      'evaluated_at']
            
            results = []
            for row in rows:
                results.append(dict(zip(columns, row)))

            return results

        except Exception as e:
            self.logger.error(f"获取性能历史失败: {e}")
            return []

    def get_accuracy_statistics(self, days: int = 30) -> Dict[str, float]:
        """获取准确率统计"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取最近N天的性能数据
            cursor.execute("""
                SELECT model_type, AVG(accuracy) as avg_accuracy, AVG(mae) as avg_mae,
                       AVG(accuracy_1) as avg_accuracy_1, AVG(accuracy_2) as avg_accuracy_2
                FROM sum_model_performance 
                WHERE evaluated_at >= datetime('now', '-{} days')
                GROUP BY model_type
            """.format(days))

            rows = cursor.fetchall()
            conn.close()

            results = {}
            for row in rows:
                results[row[0]] = {
                    'avg_accuracy': row[1],
                    'avg_mae': row[2],
                    'avg_accuracy_1': row[3],
                    'avg_accuracy_2': row[4]
                }

            return results

        except Exception as e:
            self.logger.error(f"获取准确率统计失败: {e}")
            return {}

    def update_sum_distribution(self, sum_stats: Dict[int, Dict]) -> bool:
        """更新和值分布统计"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清空旧数据
            cursor.execute("DELETE FROM sum_distribution_stats")

            # 插入新数据
            for sum_value, stats in sum_stats.items():
                cursor.execute("""
                    INSERT INTO sum_distribution_stats
                    (sum_value, frequency, probability, avg_span, common_patterns,
                     seasonal_frequency, correlation_with_positions)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    sum_value,
                    stats['frequency'],
                    stats['probability'],
                    stats.get('avg_span', 0.0),
                    json.dumps(stats.get('common_patterns', [])),
                    json.dumps(stats.get('seasonal_frequency', {})),
                    json.dumps(stats.get('correlation_with_positions', {}))
                ))

            conn.commit()
            conn.close()

            self.logger.info("和值分布统计更新成功")
            return True

        except Exception as e:
            self.logger.error(f"更新和值分布统计失败: {e}")
            return False

    def get_constraint_rules(self) -> List[Dict[str, Any]]:
        """获取约束规则"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM sum_constraint_rules 
                WHERE is_active = 1 
                ORDER BY priority DESC
            """)

            rows = cursor.fetchall()
            conn.close()

            # 转换为字典列表
            columns = ['id', 'rule_name', 'rule_description', 'rule_type', 'min_sum', 'max_sum',
                      'span_constraint', 'pattern_constraint', 'position_constraint',
                      'weight', 'priority', 'is_active', 'created_at', 'updated_at']
            
            results = []
            for row in rows:
                rule_dict = dict(zip(columns, row))
                # 解析JSON字段
                for json_field in ['span_constraint', 'pattern_constraint', 'position_constraint']:
                    if rule_dict[json_field]:
                        rule_dict[json_field] = json.loads(rule_dict[json_field])
                results.append(rule_dict)

            return results

        except Exception as e:
            self.logger.error(f"获取约束规则失败: {e}")
            return []

    def load_lottery_data(self, limit: Optional[int] = None) -> pd.DataFrame:
        """
        加载彩票历史数据并转换为标准格式

        Args:
            limit: 限制返回的记录数

        Returns:
            包含历史数据的DataFrame
        """
        try:
            conn = sqlite3.connect(self.db_path)

            # 构建查询语句
            if limit:
                query = """
                    SELECT period, numbers, date
                    FROM lottery_records
                    ORDER BY id DESC
                    LIMIT ?
                """
                df = pd.read_sql_query(query, conn, params=(limit,))
            else:
                query = """
                    SELECT period, numbers, date
                    FROM lottery_records
                    ORDER BY id DESC
                """
                df = pd.read_sql_query(query, conn)

            conn.close()

            if df.empty:
                self.logger.warning("数据库中没有彩票数据")
                return pd.DataFrame()

            # 转换数据格式
            df = self._convert_data_format(df)

            self.logger.info(f"加载彩票数据成功: {len(df)} 条记录")
            return df

        except Exception as e:
            self.logger.error(f"加载彩票数据失败: {e}")
            return pd.DataFrame()

    def _convert_data_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        转换数据格式以匹配预期的结构

        Args:
            df: 原始数据DataFrame

        Returns:
            转换后的DataFrame
        """
        try:
            # 重命名列
            df = df.rename(columns={'period': 'issue'})

            # 解析numbers字段 (如 "123" -> hundreds=1, tens=2, units=3)
            df['hundreds'] = df['numbers'].str[0].astype(int)
            df['tens'] = df['numbers'].str[1].astype(int)
            df['units'] = df['numbers'].str[2].astype(int)

            # 计算和值
            df['sum'] = df['hundreds'] + df['tens'] + df['units']

            # 计算跨度
            df['span'] = df[['hundreds', 'tens', 'units']].max(axis=1) - df[['hundreds', 'tens', 'units']].min(axis=1)

            # 保留需要的列
            result_df = df[['issue', 'hundreds', 'tens', 'units', 'sum', 'span', 'date']].copy()

            return result_df

        except Exception as e:
            self.logger.error(f"数据格式转换失败: {e}")
            return pd.DataFrame()

    def _verify_tables(self):
        """验证必要的表是否存在"""
        required_tables = ['sum_predictions', 'sum_model_performance',
                          'sum_distribution_stats', 'sum_constraint_rules']

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for table in required_tables:
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name=?
            """, (table,))

            if not cursor.fetchone():
                self.logger.warning(f"表 {table} 不存在")
                # 这里可以选择创建表或抛出异常

        conn.close()

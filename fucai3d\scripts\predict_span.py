#!/usr/bin/env python3
"""
P7跨度预测脚本

支持单期预测和批量预测，包括双重约束和模式分析
支持多种输出格式和详细的预测信息

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import logging
import json
import time
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.span_predictor import SpanPredictor

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="P7跨度预测脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 单期预测
  python predict_span.py --db-path data/lottery.db --issue 2025001
  
  # 批量预测
  python predict_span.py --db-path data/lottery.db --batch --start-issue 2025001 --count 10
  
  # 使用指定模型
  python predict_span.py --db-path data/lottery.db --issue 2025001 --model ensemble
  
  # 启用双重约束
  python predict_span.py --db-path data/lottery.db --issue 2025001 --enable-constraints
  
  # 详细输出
  python predict_span.py --db-path data/lottery.db --issue 2025001 --verbose --output result.json
        """
    )
    
    # 必需参数
    parser.add_argument(
        "--db-path",
        type=str,
        required=True,
        help="数据库文件路径"
    )
    
    # 预测模式
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        "--issue",
        type=str,
        help="单期预测的期号"
    )
    group.add_argument(
        "--batch",
        action="store_true",
        help="批量预测模式"
    )
    
    # 批量预测参数
    parser.add_argument(
        "--start-issue",
        type=str,
        help="批量预测起始期号"
    )
    parser.add_argument(
        "--count",
        type=int,
        default=10,
        help="批量预测期数 (默认: 10)"
    )
    
    # 模型选择
    parser.add_argument(
        "--model",
        choices=["xgb", "lgb", "lstm", "classification", "constraint", "ensemble"],
        default="ensemble",
        help="使用的模型 (默认: ensemble)"
    )
    
    parser.add_argument(
        "--model-dir",
        type=str,
        default="models/span_predictor",
        help="模型文件目录 (默认: models/span_predictor)"
    )
    
    # 功能开关
    parser.add_argument(
        "--enable-constraints",
        action="store_true",
        help="启用双重约束优化"
    )
    
    parser.add_argument(
        "--enable-patterns",
        action="store_true",
        help="启用模式分析"
    )
    
    parser.add_argument(
        "--all-models",
        action="store_true",
        help="使用所有模型进行预测"
    )
    
    # 输出选项
    parser.add_argument(
        "--output",
        type=str,
        help="输出文件路径 (JSON格式)"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--format",
        choices=["table", "json", "csv"],
        default="table",
        help="输出格式 (默认: table)"
    )
    
    parser.add_argument(
        "--config-path",
        type=str,
        default="config/span_predictor_config.yaml",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="WARNING",
        help="日志级别 (默认: WARNING)"
    )
    
    return parser.parse_args()

def validate_arguments(args):
    """验证命令行参数"""
    # 检查数据库文件
    if not Path(args.db_path).exists():
        raise FileNotFoundError(f"数据库文件不存在: {args.db_path}")
    
    # 检查模型目录
    if not Path(args.model_dir).exists():
        raise FileNotFoundError(f"模型目录不存在: {args.model_dir}")
    
    # 批量预测参数检查
    if args.batch and not args.start_issue:
        raise ValueError("批量预测模式需要指定起始期号 (--start-issue)")

def load_predictor_models(predictor: SpanPredictor, model_dir: str, models_to_load: List[str]) -> Dict[str, bool]:
    """加载预测器模型"""
    print("加载模型...")
    
    load_results = {}
    for model_name in models_to_load:
        model_file = Path(model_dir) / f"{model_name}_span_model.pkl"
        
        if model_file.exists():
            try:
                success = predictor.load_model(model_name, str(model_file))
                load_results[model_name] = success
                status = "✓" if success else "✗"
                print(f"  {status} {model_name}: {model_file}")
            except Exception as e:
                load_results[model_name] = False
                print(f"  ✗ {model_name}: {str(e)}")
        else:
            load_results[model_name] = False
            print(f"  - {model_name}: 文件不存在")
    
    successful_loads = sum(load_results.values())
    print(f"成功加载 {successful_loads}/{len(models_to_load)} 个模型")
    
    return load_results

def predict_single_issue(predictor: SpanPredictor, issue: str, args) -> Dict[str, Any]:
    """单期预测"""
    print(f"\n预测期号: {issue}")
    print("-" * 40)
    
    start_time = time.time()
    
    try:
        if args.enable_constraints:
            # 使用双重约束预测
            result = predictor.predict_next_period_with_constraints(issue)
        else:
            # 基础预测
            features = predictor._get_prediction_features(issue)
            X = np.array(features).reshape(1, -1)
            
            predictions, confidences = predictor.predict_with_confidence(X)
            
            result = {
                'issue': issue,
                'position': 'span',
                'base_prediction': float(predictions[0]),
                'base_confidence': float(confidences[0]),
                'model_type': predictor.current_model,
                'dual_constraint_enabled': False
            }
        
        # 添加模式分析
        if args.enable_patterns:
            try:
                pattern_analysis = predictor.analyze_comprehensive_patterns(issue)
                result['pattern_analysis'] = pattern_analysis
            except Exception as e:
                result['pattern_analysis_error'] = str(e)
        
        # 添加所有模型预测
        if args.all_models:
            try:
                features = predictor._get_prediction_features(issue)
                X = np.array(features).reshape(1, -1)
                all_predictions = predictor.predict_with_all_models(X)
                result['all_model_predictions'] = {k: float(v[0]) for k, v in all_predictions.items()}
            except Exception as e:
                result['all_models_error'] = str(e)
        
        result['prediction_time'] = time.time() - start_time
        
        return result
        
    except Exception as e:
        return {
            'issue': issue,
            'error': str(e),
            'prediction_time': time.time() - start_time
        }

def predict_batch_issues(predictor: SpanPredictor, start_issue: str, count: int, args) -> List[Dict[str, Any]]:
    """批量预测"""
    print(f"\n批量预测: 从 {start_issue} 开始，预测 {count} 期")
    print("-" * 50)
    
    results = []
    
    # 生成期号列表
    issues = generate_issue_list(start_issue, count)
    
    for i, issue in enumerate(issues, 1):
        print(f"[{i}/{count}] 预测期号: {issue}")
        
        result = predict_single_issue(predictor, issue, args)
        results.append(result)
        
        # 显示简要结果
        if 'error' not in result:
            prediction = result.get('optimized_prediction', result.get('base_prediction', 0))
            confidence = result.get('base_confidence', 0)
            print(f"  预测跨度: {prediction:.2f}, 置信度: {confidence:.3f}")
        else:
            print(f"  错误: {result['error']}")
    
    return results

def generate_issue_list(start_issue: str, count: int) -> List[str]:
    """生成期号列表"""
    # 简单的期号生成逻辑
    # 假设期号格式为 YYYYNNN
    try:
        year = int(start_issue[:4])
        number = int(start_issue[4:])
        
        issues = []
        for i in range(count):
            current_number = number + i
            if current_number > 365:  # 假设一年最多365期
                year += 1
                current_number = 1
            
            issue = f"{year}{current_number:03d}"
            issues.append(issue)
        
        return issues
    except:
        # 如果解析失败，使用简单递增
        base_num = int(start_issue) if start_issue.isdigit() else 2025001
        return [str(base_num + i) for i in range(count)]

def format_output(results: List[Dict[str, Any]], format_type: str, verbose: bool) -> str:
    """格式化输出"""
    if format_type == "json":
        return json.dumps(results, indent=2, ensure_ascii=False)
    
    elif format_type == "csv":
        import csv
        import io
        
        output = io.StringIO()
        if results:
            fieldnames = ['issue', 'prediction', 'confidence', 'model', 'error']
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in results:
                row = {
                    'issue': result.get('issue', ''),
                    'prediction': result.get('optimized_prediction', result.get('base_prediction', '')),
                    'confidence': result.get('base_confidence', ''),
                    'model': result.get('model_type', ''),
                    'error': result.get('error', '')
                }
                writer.writerow(row)
        
        return output.getvalue()
    
    else:  # table format
        output = []
        output.append("跨度预测结果")
        output.append("=" * 60)
        
        for result in results:
            output.append(f"\n期号: {result.get('issue', 'N/A')}")
            
            if 'error' in result:
                output.append(f"错误: {result['error']}")
                continue
            
            prediction = result.get('optimized_prediction', result.get('base_prediction', 0))
            confidence = result.get('base_confidence', 0)
            model = result.get('model_type', 'unknown')
            
            output.append(f"预测跨度: {prediction:.2f}")
            output.append(f"置信度: {confidence:.3f}")
            output.append(f"使用模型: {model}")
            
            if result.get('dual_constraint_enabled'):
                constraint_info = result.get('constraint_info', {})
                if constraint_info:
                    output.append(f"位置一致性: {constraint_info.get('position_consistency', 0):.3f}")
                    output.append(f"和值一致性: {constraint_info.get('sum_consistency', 0):.3f}")
                    output.append(f"双重一致性: {constraint_info.get('dual_consistency', 0):.3f}")
            
            if verbose and 'all_model_predictions' in result:
                output.append("所有模型预测:")
                for model_name, pred in result['all_model_predictions'].items():
                    output.append(f"  {model_name}: {pred:.2f}")
            
            if verbose and 'pattern_analysis' in result:
                pattern = result['pattern_analysis']
                if 'basic_statistics' in pattern:
                    stats = pattern['basic_statistics']
                    output.append(f"历史平均跨度: {stats.get('mean', 0):.2f}")
                    output.append(f"跨度方差: {stats.get('std', 0):.2f}")
            
            prediction_time = result.get('prediction_time', 0)
            output.append(f"预测用时: {prediction_time:.3f}秒")
            output.append("-" * 40)
        
        return "\n".join(output)

def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志
        setup_logging(args.log_level)
        
        # 验证参数
        validate_arguments(args)
        
        # 初始化预测器
        print("初始化跨度预测器...")
        predictor = SpanPredictor(args.db_path, args.config_path)
        
        # 配置预测器
        if args.enable_constraints:
            predictor.enable_dual_constraints(True)
        if args.enable_patterns:
            predictor.enable_pattern_analysis(True)
        
        # 构建模型
        if not predictor.build_model():
            raise RuntimeError("模型构建失败")
        
        # 确定要加载的模型
        models_to_load = [args.model] if not args.all_models else list(predictor.models.keys())
        
        # 加载模型
        load_results = load_predictor_models(predictor, args.model_dir, models_to_load)
        
        # 检查是否有可用模型
        if not any(load_results.values()):
            raise RuntimeError("没有可用的训练模型")
        
        # 设置当前模型
        if args.model in predictor.models and load_results.get(args.model, False):
            predictor.switch_model(args.model)
        else:
            # 使用第一个成功加载的模型
            for model_name, success in load_results.items():
                if success:
                    predictor.switch_model(model_name)
                    break
        
        print(f"当前使用模型: {predictor.current_model}")
        
        # 执行预测
        if args.batch:
            results = predict_batch_issues(predictor, args.start_issue, args.count, args)
        else:
            result = predict_single_issue(predictor, args.issue, args)
            results = [result]
        
        # 格式化输出
        output_text = format_output(results, args.format, args.verbose)
        
        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(output_text)
            print(f"\n结果已保存到: {args.output}")
        else:
            print(f"\n{output_text}")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n⚠️  预测被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 预测失败: {str(e)}")
        logging.error(f"预测脚本执行失败: {e}")
        return 1

if __name__ == "__main__":
    import numpy as np  # 需要在这里导入
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
P7跨度预测器集成测试

快速验证P7跨度预测器的核心功能
"""

import sys
import os
import tempfile
import sqlite3
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_database():
    """创建测试数据库"""
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    db_path = temp_db.name
    temp_db.close()
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建基础表
    cursor.execute('''
        CREATE TABLE lottery_data (
            id INTEGER PRIMARY KEY,
            issue TEXT,
            hundreds INTEGER,
            tens INTEGER,
            units INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 插入测试数据
    test_data = []
    np.random.seed(42)
    for i in range(100):
        issue = f'2024{i+1:03d}'
        hundreds = np.random.randint(0, 10)
        tens = np.random.randint(0, 10)
        units = np.random.randint(0, 10)
        test_data.append((issue, hundreds, tens, units))
    
    cursor.executemany(
        'INSERT INTO lottery_data (issue, hundreds, tens, units) VALUES (?, ?, ?, ?)',
        test_data
    )
    
    conn.commit()
    conn.close()
    
    return db_path

def test_span_data_access():
    """测试跨度数据访问层"""
    print("测试跨度数据访问层...")
    
    try:
        from src.data.span_data_access import SpanDataAccess
        
        db_path = create_test_database()
        data_access = SpanDataAccess(db_path)
        
        # 测试加载数据
        data = data_access.load_lottery_data()
        assert not data.empty, "数据加载失败"
        assert len(data) == 100, f"数据数量不正确: {len(data)}"
        
        # 测试模式分析
        result = data_access.analyze_span_patterns(1, 2, 3)
        assert result['span'] == 2, "跨度计算错误"
        assert result['patterns']['ascending'], "升序模式识别错误"
        
        print("✓ 跨度数据访问层测试通过")
        
        # 清理
        os.unlink(db_path)
        return True
        
    except Exception as e:
        print(f"✗ 跨度数据访问层测试失败: {e}")
        return False

def test_span_predictor_basic():
    """测试跨度预测器基础功能"""
    print("测试跨度预测器基础功能...")
    
    try:
        from src.predictors.span_predictor import SpanPredictor
        
        db_path = create_test_database()
        predictor = SpanPredictor(db_path)
        
        # 测试初始化
        assert predictor.position == 'span', "位置设置错误"
        assert predictor.prediction_range == (0, 9), "预测范围错误"
        
        # 测试模型构建
        success = predictor.build_model()
        assert success, "模型构建失败"
        
        # 测试模型列表
        models = predictor.get_available_models()
        expected_models = ['xgb', 'lgb', 'lstm', 'classification', 'constraint', 'ensemble']
        for model in expected_models:
            assert model in models, f"缺少模型: {model}"
        
        # 测试模型切换
        predictor.switch_model('ensemble')
        assert predictor.current_model == 'ensemble', "模型切换失败"
        
        # 测试配置
        predictor.enable_dual_constraints(True)
        predictor.enable_pattern_analysis(True)
        
        config = predictor.get_constraint_configuration()
        assert config['dual_constraints_enabled'], "双重约束配置失败"
        assert config['pattern_analysis_enabled'], "模式分析配置失败"
        
        print("✓ 跨度预测器基础功能测试通过")
        
        # 清理
        os.unlink(db_path)
        return True
        
    except Exception as e:
        print(f"✗ 跨度预测器基础功能测试失败: {e}")
        return False

def test_span_models():
    """测试跨度预测模型"""
    print("测试跨度预测模型...")
    
    try:
        db_path = create_test_database()
        
        # 测试基础模型类
        from src.predictors.models.base_span_model import BaseSpanModel
        
        # 测试XGBoost模型
        try:
            from src.predictors.models.xgb_span_model import XGBSpanModel
            xgb_model = XGBSpanModel(db_path)
            assert xgb_model.model_type == 'xgb', "XGBoost模型类型错误"
            success = xgb_model.build_model()
            assert success, "XGBoost模型构建失败"
            print("  ✓ XGBoost模型测试通过")
        except ImportError:
            print("  - XGBoost未安装，跳过测试")
        
        # 测试LightGBM模型
        try:
            from src.predictors.models.lgb_span_model import LGBSpanModel
            lgb_model = LGBSpanModel(db_path)
            assert lgb_model.model_type == 'lgb', "LightGBM模型类型错误"
            success = lgb_model.build_model()
            assert success, "LightGBM模型构建失败"
            print("  ✓ LightGBM模型测试通过")
        except ImportError:
            print("  - LightGBM未安装，跳过测试")
        
        # 测试LSTM模型
        try:
            from src.predictors.models.lstm_span_model import LSTMSpanModel
            lstm_model = LSTMSpanModel(db_path)
            assert lstm_model.model_type == 'lstm', "LSTM模型类型错误"
            success = lstm_model.build_model()
            assert success, "LSTM模型构建失败"
            print("  ✓ LSTM模型测试通过")
        except ImportError:
            print("  - TensorFlow未安装，跳过测试")
        
        # 测试分类模型
        try:
            from src.predictors.models.classification_span_model import ClassificationSpanModel
            cls_model = ClassificationSpanModel(db_path)
            assert cls_model.model_type == 'classification', "分类模型类型错误"
            assert cls_model.num_classes == 10, "分类数量错误"
            success = cls_model.build_model()
            assert success, "分类模型构建失败"
            print("  ✓ 分类模型测试通过")
        except ImportError:
            print("  - 分类模型依赖未安装，跳过测试")
        
        # 测试约束模型
        from src.predictors.models.constraint_span_model import ConstraintSpanModel
        constraint_model = ConstraintSpanModel(db_path)
        assert constraint_model.model_type == 'constraint', "约束模型类型错误"
        success = constraint_model.build_model()
        assert success, "约束模型构建失败"
        print("  ✓ 约束模型测试通过")
        
        # 测试集成模型
        from src.predictors.models.ensemble_span_model import EnsembleSpanModel
        ensemble_model = EnsembleSpanModel(db_path)
        assert ensemble_model.model_type == 'ensemble', "集成模型类型错误"
        success = ensemble_model.build_model()
        assert success, "集成模型构建失败"
        print("  ✓ 集成模型测试通过")
        
        print("✓ 跨度预测模型测试通过")
        
        # 清理
        os.unlink(db_path)
        return True
        
    except Exception as e:
        print(f"✗ 跨度预测模型测试失败: {e}")
        return False

def test_pattern_analysis():
    """测试模式分析功能"""
    print("测试模式分析功能...")
    
    try:
        from src.predictors.span_predictor import SpanPredictor
        
        db_path = create_test_database()
        predictor = SpanPredictor(db_path)
        
        # 测试模式概率预测
        result = predictor.predict_pattern_probability([1, 2, 3])
        assert result['predicted_span'] == 2, "跨度计算错误"
        assert result['ascending'] == 1.0, "升序模式识别错误"
        assert result['same_digit'] == 0.0, "相同数字模式识别错误"
        
        # 测试相同数字模式
        result = predictor.predict_pattern_probability([5, 5, 5])
        assert result['predicted_span'] == 0, "相同数字跨度计算错误"
        assert result['same_digit'] == 1.0, "相同数字模式识别错误"
        
        print("✓ 模式分析功能测试通过")
        
        # 清理
        os.unlink(db_path)
        return True
        
    except Exception as e:
        print(f"✗ 模式分析功能测试失败: {e}")
        return False

def test_constraint_functionality():
    """测试约束功能"""
    print("测试约束功能...")
    
    try:
        from src.predictors.span_predictor import SpanPredictor
        
        db_path = create_test_database()
        predictor = SpanPredictor(db_path)
        
        # 测试约束一致性评分
        position_predictions = {'hundreds': 1, 'tens': 2, 'units': 3}
        sum_prediction = 6.0
        span_prediction = 2.0
        
        scores = predictor.calculate_constraint_consistency_score(
            span_prediction, position_predictions, sum_prediction
        )
        
        assert 'position_consistency' in scores, "缺少位置一致性评分"
        assert 'sum_consistency' in scores, "缺少和值一致性评分"
        assert 'dual_consistency' in scores, "缺少双重一致性评分"
        
        # 检查评分范围
        for key, score in scores.items():
            if isinstance(score, (int, float)):
                assert 0 <= score <= 1, f"评分超出范围: {key}={score}"
        
        print("✓ 约束功能测试通过")
        
        # 清理
        os.unlink(db_path)
        return True
        
    except Exception as e:
        print(f"✗ 约束功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("P7跨度预测器集成测试")
    print("=" * 50)
    
    tests = [
        test_span_data_access,
        test_span_predictor_basic,
        test_span_models,
        test_pattern_analysis,
        test_constraint_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！P7跨度预测器功能正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

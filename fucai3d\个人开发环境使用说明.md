# P6-P7预测器个人开发环境使用说明

## 🎯 环境要求

**Python版本**: 3.11.9 (推荐)  
**环境类型**: 个人开发环境  
**不使用**: Anaconda、Docker  
**项目路径**: D:\github\fucai3d  

## 🚀 快速开始 (3步骤)

### 步骤1: 安装依赖
```bash
# 自动安装所有依赖
python install_dependencies.py

# 或手动安装基础依赖
pip install pandas numpy pyyaml
```

### 步骤2: 测试环境
```bash
# 运行环境测试
python test_personal_env.py
```

### 步骤3: 体验功能
```bash
# 运行功能演示
python quick_demo.py
```

## 📋 详细安装指南

### 基础依赖 (必需)
```bash
pip install pandas numpy pyyaml
```

### 机器学习依赖 (可选)
```bash
pip install scikit-learn xgboost lightgbm
```

### 深度学习依赖 (可选)
```bash
pip install tensorflow
```

## 🧪 测试和验证

### 环境测试
```bash
python test_personal_env.py
```
**功能**: 全面测试Python环境、依赖包、数据库连接、项目代码

### 功能演示
```bash
python quick_demo.py
```
**功能**: 演示P6和P7预测器的核心功能和专属特征

### 预期结果
- ✅ 所有基础库导入成功
- ✅ 数据库连接正常
- ✅ 项目代码导入无错误
- ✅ 数据加载功能正常
- ✅ 预测器初始化成功

## 🎯 核心功能使用

### P6和值预测器
```bash
# 查看帮助
python scripts/train_sum_predictor.py --help

# 训练模型
python scripts/train_sum_predictor.py --db-path data/lottery.db

# 执行预测
python scripts/predict_sum.py --db-path data/lottery.db --issue 2025001
```

### P7跨度预测器
```bash
# 查看帮助
python scripts/train_span_predictor.py --help

# 训练模型
python scripts/train_span_predictor.py --db-path data/lottery.db

# 执行预测 (带约束优化)
python scripts/predict_span.py --db-path data/lottery.db --issue 2025001 --enable-constraints

# 使用命令行工具
python scripts/span_predictor_cli.py info --db-path data/lottery.db
```

## 🌟 专属功能特色

### P6和值预测器专属功能
- **约束优化**: 基于数学约束的和值优化
- **分布预测**: 和值概率分布分析
- **数学特性**: 和值数学规律挖掘

### P7跨度预测器专属功能
- **双重约束优化**: 与P3-P5位置预测器和P6和值预测器协同
- **模式分析**: 升序、降序、相同数字等模式识别
- **分类预测**: 10分类问题，Top-K预测
- **约束一致性评分**: 多维度约束评估

## 📊 使用示例

### 基础使用示例
```python
from src.predictors.sum_predictor import SumPredictor
from src.predictors.span_predictor import SpanPredictor

# P6和值预测器
sum_predictor = SumPredictor('data/lottery.db')
sum_predictor.build_model()
# 训练和预测...

# P7跨度预测器
span_predictor = SpanPredictor('data/lottery.db')
span_predictor.enable_dual_constraints(True)
span_predictor.enable_pattern_analysis(True)
span_predictor.build_model()
# 训练和预测...
```

### 高级功能示例
```python
# P7模式分析
result = span_predictor.predict_pattern_probability([1, 2, 3])
print(f"跨度: {result['predicted_span']}")
print(f"升序模式: {result['ascending']}")

# P7约束一致性评分
scores = span_predictor.calculate_constraint_consistency_score(
    span_prediction=2.0,
    position_predictions={'hundreds': 1, 'tens': 2, 'units': 3},
    sum_prediction=6.0
)
print(f"约束一致性: {scores}")
```

## 🔧 故障排除

### 常见问题

#### 问题1: pandas导入失败
```bash
# 解决方案
pip install --upgrade pandas
# 或重新安装
pip uninstall pandas && pip install pandas
```

#### 问题2: 项目代码导入失败
```bash
# 确认在项目根目录
cd D:\github\fucai3d
python -c "import os; print(os.getcwd())"
```

#### 问题3: 数据库连接失败
```bash
# 检查数据库文件
python -c "from pathlib import Path; print(Path('data/lottery.db').exists())"
```

#### 问题4: 机器学习库缺失
```bash
# 安装可选依赖
pip install scikit-learn xgboost lightgbm tensorflow
```

### 环境检查命令
```bash
# 检查Python版本
python --version

# 检查pip版本
pip --version

# 检查已安装包
pip list | findstr "pandas numpy yaml"

# 检查项目文件
dir src\predictors\*.py
```

## 📚 文档资源

### 技术文档
- `docs/P7_SPAN_PREDICTOR_README.md` - P7详细使用指南
- `个人开发环境测试指南.md` - 详细测试说明
- `项目管理文档/` - 完整的项目文档

### 配置文件
- `config/sum_predictor_config.yaml` - P6配置
- `config/span_predictor_config.yaml` - P7配置

### 测试文件
- `tests/test_sum_predictor.py` - P6测试套件
- `tests/test_span_predictor.py` - P7测试套件

## 🎖️ 成功标准

### 环境就绪标准
- ✅ Python 3.11.x 正常运行
- ✅ 基础依赖 (pandas, numpy, pyyaml) 安装成功
- ✅ 数据库文件存在且可访问
- ✅ 项目代码导入无错误

### 功能验证标准
- ✅ 数据加载返回正确格式的DataFrame
- ✅ 预测器初始化成功，属性设置正确
- ✅ 专属功能 (约束优化、模式分析) 正常工作
- ✅ 配置文件读取正常

### 使用就绪标准
- ✅ 训练脚本可以正常运行
- ✅ 预测脚本可以正常运行
- ✅ 命令行工具响应正常
- ✅ 文档和示例可以正常访问

## 🚀 性能优化建议

### 开发环境优化
1. **使用SSD硬盘**: 提升数据加载速度
2. **充足内存**: 推荐8GB+内存
3. **Python环境**: 使用最新的Python 3.11.x
4. **IDE选择**: 推荐PyCharm或VSCode

### 代码运行优化
1. **数据缓存**: 避免重复加载大量数据
2. **模型选择**: 根据需求选择合适的模型
3. **并行处理**: 利用多核CPU加速训练
4. **内存管理**: 及时释放不需要的变量

## 📞 技术支持

### 自助解决
1. 运行 `python test_personal_env.py` 诊断问题
2. 查看错误信息和堆栈跟踪
3. 参考故障排除部分
4. 检查依赖包安装状态

### 文档参考
- 详细API文档: 代码内注释
- 使用示例: `quick_demo.py`
- 配置说明: YAML文件注释
- 项目文档: `项目管理文档/` 目录

---

**重要提示**: 这个使用说明专门为个人Python 3.11.9开发环境设计，避免了Anaconda和Docker的复杂性，专注于核心功能的快速使用。

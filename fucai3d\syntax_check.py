#!/usr/bin/env python3
"""
语法检查脚本 - 验证P6和P7预测器代码语法
"""

import ast
import sys
from pathlib import Path

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 编译检查语法
        ast.parse(source)
        return True, None
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def main():
    """主函数"""
    print("🔍 P6-P7预测器语法检查")
    print("=" * 50)
    
    # 需要检查的关键文件
    files_to_check = [
        "src/predictors/sum_predictor.py",
        "src/predictors/span_predictor.py", 
        "src/data/sum_data_access.py",
        "src/data/span_data_access.py",
        "scripts/train_sum_predictor.py",
        "scripts/train_span_predictor.py",
        "scripts/predict_sum.py",
        "scripts/predict_span.py",
        "tests/test_sum_predictor.py",
        "tests/test_span_predictor.py"
    ]
    
    passed = 0
    failed = 0
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            success, error = check_syntax(file_path)
            if success:
                print(f"✅ {file_path}")
                passed += 1
            else:
                print(f"❌ {file_path}: {error}")
                failed += 1
        else:
            print(f"⚠️ {file_path}: 文件不存在")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"语法检查结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有文件语法检查通过！")
        return 0
    else:
        print("⚠️ 发现语法错误，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
